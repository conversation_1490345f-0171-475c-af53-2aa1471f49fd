import 'package:flutter/foundation.dart';
import '../models/order.dart';
import '../models/user.dart';
import '../models/cart.dart';

class OrderProvider with ChangeNotifier {
  List<Order> _orders = [];
  bool _isLoading = false;
  String? _error;

  List<Order> get orders => _orders;
  List<Order> get activeOrders => _orders.where((order) => 
    order.status == OrderStatus.placed ||
    order.status == OrderStatus.confirmed ||
    order.status == OrderStatus.preparing ||
    order.status == OrderStatus.onTheWay
  ).toList();
  
  List<Order> get pastOrders => _orders.where((order) => 
    order.status == OrderStatus.delivered
  ).toList();
  
  List<Order> get cancelledOrders => _orders.where((order) => 
    order.status == OrderStatus.cancelled
  ).toList();

  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> loadOrders() async {
    _setLoading(true);
    _error = null;

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      // Mock order data
      _orders = _generateMockOrders();

      _setLoading(false);
    } catch (e) {
      _error = 'Failed to load orders';
      _setLoading(false);
      debugPrint('Error loading orders: $e');
    }
  }

  Future<bool> placeOrder(Order order) async {
    _setLoading(true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      _orders.insert(0, order);
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setLoading(false);
      debugPrint('Error placing order: $e');
      return false;
    }
  }

  Future<bool> cancelOrder(String orderId) async {
    _setLoading(true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      final orderIndex = _orders.indexWhere((order) => order.id == orderId);
      if (orderIndex != -1) {
        _orders[orderIndex] = _orders[orderIndex].copyWith(
          status: OrderStatus.cancelled,
        );
        _setLoading(false);
        notifyListeners();
        return true;
      }

      _setLoading(false);
      return false;
    } catch (e) {
      _setLoading(false);
      debugPrint('Error cancelling order: $e');
      return false;
    }
  }

  Future<bool> reorder(String orderId) async {
    _setLoading(true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      final originalOrder = _orders.firstWhere((order) => order.id == orderId);
      final newOrder = originalOrder.copyWith(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        status: OrderStatus.placed,
        orderTime: DateTime.now(),
      );

      _orders.insert(0, newOrder);
      _setLoading(false);
      notifyListeners();
      return true;
    } catch (e) {
      _setLoading(false);
      debugPrint('Error reordering: $e');
      return false;
    }
  }

  Order? getOrderById(String orderId) {
    try {
      return _orders.firstWhere((order) => order.id == orderId);
    } catch (e) {
      return null;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  List<Order> _generateMockOrders() {
    return [
      Order(
        id: '1',
        userId: '1',
        restaurantId: '1',
        restaurantName: 'Pizza Palace',
        items: [
          CartItem(
            id: '1',
            menuItemId: 'menu_1',
            restaurantId: '1',
            restaurantName: 'Pizza Palace',
            name: 'Margherita Pizza',
            description: 'Classic pizza with tomato sauce, mozzarella, and basil',
            basePrice: 12.99,
            quantity: 1,
            imageUrl: 'https://via.placeholder.com/80x80/FF6B6B/FFFFFF?text=M',
          ),
        ],
        deliveryAddress: Address(
          id: '1',
          label: 'Home',
          addressLine1: '123 Main St',
          addressLine2: 'Apt 4B',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          latitude: 40.7128,
          longitude: -74.0060,
        ),
        status: OrderStatus.onTheWay,
        paymentMethod: PaymentMethod.creditCard,
        subtotal: 28.98,
        deliveryFee: 2.99,
        serviceFee: 0.99,
        tax: 2.32,
        discount: 0.0,
        total: 34.29,
        orderTime: DateTime.now().subtract(const Duration(minutes: 25)),
        estimatedDeliveryTime: DateTime.now().add(const Duration(minutes: 10)),
      ),
    ];
  }
}

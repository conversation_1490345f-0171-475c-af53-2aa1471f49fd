import 'package:flutter/foundation.dart';
import '../models/restaurant.dart';

class RestaurantProvider with ChangeNotifier {
  List<Restaurant> _restaurants = [];
  List<Restaurant> _featuredRestaurants = [];
  bool _isLoading = false;
  String? _error;

  List<Restaurant> get restaurants => _restaurants;
  List<Restaurant> get featuredRestaurants => _featuredRestaurants;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> loadRestaurants() async {
    _setLoading(true);
    _error = null;

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Mock restaurant data
      _restaurants = _generateMockRestaurants();
      _featuredRestaurants =
          _restaurants.where((r) => r.tags.contains('Featured')).toList();

      _setLoading(false);
    } catch (e) {
      _error = 'Failed to load restaurants';
      _setLoading(false);
      debugPrint('Error loading restaurants: $e');
    }
  }

  List<Restaurant> getRestaurantsByCategory(String category) {
    if (category == 'All') return _restaurants;
    return _restaurants.where((restaurant) {
      return restaurant.cuisine
              .toLowerCase()
              .contains(category.toLowerCase()) ||
          restaurant.tags
              .any((tag) => tag.toLowerCase().contains(category.toLowerCase()));
    }).toList();
  }

  List<Restaurant> searchRestaurants(String query) {
    if (query.isEmpty) return _restaurants;

    final lowercaseQuery = query.toLowerCase();
    return _restaurants.where((restaurant) {
      return restaurant.name.toLowerCase().contains(lowercaseQuery) ||
          restaurant.cuisine.toLowerCase().contains(lowercaseQuery) ||
          restaurant.tags
              .any((tag) => tag.toLowerCase().contains(lowercaseQuery)) ||
          restaurant.menuCategories.any((category) => category.items.any(
              (item) =>
                  item.name.toLowerCase().contains(lowercaseQuery) ||
                  item.description.toLowerCase().contains(lowercaseQuery)));
    }).toList();
  }

  Restaurant? getRestaurantById(String id) {
    try {
      return _restaurants.firstWhere((restaurant) => restaurant.id == id);
    } catch (e) {
      return null;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  List<Restaurant> _generateMockRestaurants() {
    return [
      Restaurant(
        id: '1',
        name: 'Pizza Palace',
        description: 'Authentic Italian pizzas with fresh ingredients',
        cuisine: 'Italian',
        imageUrl:
            'https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=Pizza+Palace',
        imageGallery: [
          'https://via.placeholder.com/400x300/FF6B6B/FFFFFF?text=Pizza+1',
          'https://via.placeholder.com/400x300/FF6B6B/FFFFFF?text=Pizza+2',
          'https://via.placeholder.com/400x300/FF6B6B/FFFFFF?text=Pizza+3',
        ],
        rating: 4.5,
        reviewCount: 1250,
        address: '123 Main St, Downtown',
        latitude: 40.7128,
        longitude: -74.0060,
        phone: '+1234567890',
        email: '<EMAIL>',
        estimatedDeliveryTime: 30,
        minimumOrderValue: 15.0,
        deliveryFee: 2.99,
        isOpen: true,
        openingHours: '10:00 AM',
        closingHours: '11:00 PM',
        tags: ['Featured', 'Popular', 'Fast Food'],
        discountPercentage: 20.0,
        menuCategories: [
          MenuCategory(
            id: '1',
            name: 'Pizzas',
            description: 'Delicious handcrafted pizzas with premium toppings',
            items: [
              MenuItem(
                id: '1',
                name: 'Margherita Pizza',
                description:
                    'Classic pizza with tomato sauce, mozzarella, and basil',
                price: 12.99,
                imageUrl:
                    'https://via.placeholder.com/200x150/FF6B6B/FFFFFF?text=Margherita',
                isVegetarian: true,
                isAvailable: true,
              ),
              MenuItem(
                id: '2',
                name: 'Pepperoni Pizza',
                description:
                    'Pizza with pepperoni, mozzarella, and tomato sauce',
                price: 15.99,
                imageUrl:
                    'https://via.placeholder.com/200x150/FF6B6B/FFFFFF?text=Pepperoni',
                isVegetarian: false,
                isAvailable: true,
              ),
            ],
          ),
        ],
        createdAt: DateTime.now(),
      ),
      Restaurant(
        id: '2',
        name: 'Burger House',
        description: 'Juicy burgers made with premium beef',
        cuisine: 'American',
        imageUrl:
            'https://via.placeholder.com/300x200/4ECDC4/FFFFFF?text=Burger+House',
        imageGallery: [
          'https://via.placeholder.com/400x300/4ECDC4/FFFFFF?text=Burger+1',
          'https://via.placeholder.com/400x300/4ECDC4/FFFFFF?text=Burger+2',
        ],
        rating: 4.3,
        reviewCount: 890,
        address: '456 Oak Ave, Midtown',
        latitude: 40.7589,
        longitude: -73.9851,
        phone: '+1234567891',
        email: '<EMAIL>',
        estimatedDeliveryTime: 25,
        minimumOrderValue: 12.0,
        deliveryFee: 1.99,
        isOpen: true,
        openingHours: '11:00 AM',
        closingHours: '10:00 PM',
        tags: ['Featured', 'Fast Food'],
        discountPercentage: 15.0,
        menuCategories: [
          MenuCategory(
            id: '2',
            name: 'Burgers',
            description: 'Juicy gourmet burgers with fresh toppings',
            items: [
              MenuItem(
                id: '3',
                name: 'Classic Burger',
                description:
                    'Beef patty with lettuce, tomato, and special sauce',
                price: 9.99,
                imageUrl:
                    'https://via.placeholder.com/200x150/4ECDC4/FFFFFF?text=Classic',
                isVegetarian: false,
                isAvailable: true,
              ),
              MenuItem(
                id: '4',
                name: 'Veggie Burger',
                description: 'Plant-based patty with fresh vegetables',
                price: 8.99,
                imageUrl:
                    'https://via.placeholder.com/200x150/4ECDC4/FFFFFF?text=Veggie',
                isVegetarian: true,
                isAvailable: true,
              ),
            ],
          ),
        ],
        createdAt: DateTime.now(),
      ),
      Restaurant(
        id: '3',
        name: 'Spice Garden',
        description: 'Authentic Indian cuisine with traditional spices',
        cuisine: 'Indian',
        imageUrl:
            'https://via.placeholder.com/300x200/45B7D1/FFFFFF?text=Spice+Garden',
        imageGallery: [
          'https://via.placeholder.com/400x300/45B7D1/FFFFFF?text=Indian+1',
          'https://via.placeholder.com/400x300/45B7D1/FFFFFF?text=Indian+2',
        ],
        rating: 4.7,
        reviewCount: 2100,
        address: '789 Curry Lane, Little India',
        latitude: 40.7505,
        longitude: -73.9934,
        phone: '+1234567892',
        email: '<EMAIL>',
        estimatedDeliveryTime: 35,
        minimumOrderValue: 18.0,
        deliveryFee: 3.49,
        isOpen: true,
        openingHours: '12:00 PM',
        closingHours: '11:30 PM',
        tags: ['Popular', 'Spicy', 'Vegetarian Options'],
        menuCategories: [
          MenuCategory(
            id: '3',
            name: 'Main Course',
            description: 'Authentic Indian main dishes with traditional spices',
            items: [
              MenuItem(
                id: '5',
                name: 'Butter Chicken',
                description: 'Creamy tomato-based curry with tender chicken',
                price: 16.99,
                imageUrl:
                    'https://via.placeholder.com/200x150/45B7D1/FFFFFF?text=Butter+Chicken',
                isVegetarian: false,
                isSpicy: true,
                isAvailable: true,
              ),
              MenuItem(
                id: '6',
                name: 'Paneer Tikka Masala',
                description: 'Cottage cheese in rich tomato gravy',
                price: 14.99,
                imageUrl:
                    'https://via.placeholder.com/200x150/45B7D1/FFFFFF?text=Paneer+Tikka',
                isVegetarian: true,
                isSpicy: true,
                isAvailable: true,
              ),
            ],
          ),
        ],
        createdAt: DateTime.now(),
      ),
      Restaurant(
        id: '4',
        name: 'Dragon Wok',
        description: 'Fresh Chinese dishes with authentic flavors',
        cuisine: 'Chinese',
        imageUrl:
            'https://via.placeholder.com/300x200/96CEB4/FFFFFF?text=Dragon+Wok',
        imageGallery: [
          'https://via.placeholder.com/400x300/96CEB4/FFFFFF?text=Chinese+1',
          'https://via.placeholder.com/400x300/96CEB4/FFFFFF?text=Chinese+2',
        ],
        rating: 4.4,
        reviewCount: 1560,
        address: '321 Bamboo St, Chinatown',
        latitude: 40.7155,
        longitude: -73.9976,
        phone: '+1234567893',
        email: '<EMAIL>',
        estimatedDeliveryTime: 28,
        minimumOrderValue: 16.0,
        deliveryFee: 2.49,
        isOpen: true,
        openingHours: '11:30 AM',
        closingHours: '10:30 PM',
        tags: ['Popular', 'Quick Service'],
        menuCategories: [
          MenuCategory(
            id: '4',
            name: 'Noodles & Rice',
            description: 'Traditional Chinese noodle and rice dishes',
            items: [
              MenuItem(
                id: '7',
                name: 'Kung Pao Chicken',
                description:
                    'Spicy stir-fried chicken with peanuts and vegetables',
                price: 13.99,
                imageUrl:
                    'https://via.placeholder.com/200x150/96CEB4/FFFFFF?text=Kung+Pao',
                isVegetarian: false,
                isSpicy: true,
                isAvailable: true,
              ),
              MenuItem(
                id: '8',
                name: 'Vegetable Fried Rice',
                description:
                    'Wok-fried rice with mixed vegetables and soy sauce',
                price: 10.99,
                imageUrl:
                    'https://via.placeholder.com/200x150/96CEB4/FFFFFF?text=Fried+Rice',
                isVegetarian: true,
                isAvailable: true,
              ),
            ],
          ),
        ],
        createdAt: DateTime.now(),
      ),
      Restaurant(
        id: '5',
        name: 'Taco Fiesta',
        description: 'Authentic Mexican street food and tacos',
        cuisine: 'Mexican',
        imageUrl:
            'https://via.placeholder.com/300x200/FFEAA7/000000?text=Taco+Fiesta',
        imageGallery: [
          'https://via.placeholder.com/400x300/FFEAA7/000000?text=Taco+1',
          'https://via.placeholder.com/400x300/FFEAA7/000000?text=Taco+2',
        ],
        rating: 4.2,
        reviewCount: 750,
        address: '654 Salsa Ave, Mexican Quarter',
        latitude: 40.7282,
        longitude: -73.9942,
        phone: '+1234567894',
        email: '<EMAIL>',
        estimatedDeliveryTime: 22,
        minimumOrderValue: 10.0,
        deliveryFee: 1.49,
        isOpen: true,
        openingHours: '10:00 AM',
        closingHours: '12:00 AM',
        tags: ['Quick Service', 'Spicy', 'Late Night'],
        discountPercentage: 10.0,
        menuCategories: [
          MenuCategory(
            id: '5',
            name: 'Tacos',
            description: 'Authentic Mexican tacos with fresh ingredients',
            items: [
              MenuItem(
                id: '9',
                name: 'Chicken Tacos',
                description: 'Grilled chicken with fresh salsa and cilantro',
                price: 8.99,
                imageUrl:
                    'https://via.placeholder.com/200x150/FFEAA7/000000?text=Chicken+Taco',
                isVegetarian: false,
                isAvailable: true,
              ),
              MenuItem(
                id: '10',
                name: 'Veggie Tacos',
                description: 'Black beans, corn, and fresh vegetables',
                price: 7.99,
                imageUrl:
                    'https://via.placeholder.com/200x150/FFEAA7/000000?text=Veggie+Taco',
                isVegetarian: true,
                isAvailable: true,
              ),
            ],
          ),
        ],
        createdAt: DateTime.now(),
      ),
    ];
  }
}

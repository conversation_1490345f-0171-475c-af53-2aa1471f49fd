import 'cart.dart';
import 'user.dart';

enum OrderStatus {
  placed,
  confirmed,
  preparing,
  ready,
  pickedUp,
  onTheWay,
  delivered,
  cancelled,
}

enum PaymentMethod {
  creditCard,
  debitCard,
  upi,
  wallet,
  cashOnDelivery,
}

class Order {
  final String id;
  final String userId;
  final String restaurantId;
  final String restaurantName;
  final List<CartItem> items;
  final Address deliveryAddress;
  final OrderStatus status;
  final PaymentMethod paymentMethod;
  final double subtotal;
  final double deliveryFee;
  final double serviceFee;
  final double tax;
  final double discount;
  final double total;
  final String? promoCode;
  final String? specialInstructions;
  final DateTime orderTime;
  final DateTime? estimatedDeliveryTime;
  final DateTime? actualDeliveryTime;
  final DeliveryBoy? deliveryBoy;
  final List<OrderStatusUpdate> statusUpdates;
  final OrderRating? rating;

  Order({
    required this.id,
    required this.userId,
    required this.restaurantId,
    required this.restaurantName,
    required this.items,
    required this.deliveryAddress,
    required this.status,
    required this.paymentMethod,
    required this.subtotal,
    required this.deliveryFee,
    required this.serviceFee,
    required this.tax,
    required this.discount,
    required this.total,
    this.promoCode,
    this.specialInstructions,
    required this.orderTime,
    this.estimatedDeliveryTime,
    this.actualDeliveryTime,
    this.deliveryBoy,
    this.statusUpdates = const [],
    this.rating,
  });

  factory Order.fromCart(
    Cart cart,
    String orderId,
    String userId,
    Address deliveryAddress,
    PaymentMethod paymentMethod,
    String? specialInstructions,
  ) {
    return Order(
      id: orderId,
      userId: userId,
      restaurantId: cart.restaurantId,
      restaurantName: cart.restaurantName,
      items: cart.items,
      deliveryAddress: deliveryAddress,
      status: OrderStatus.placed,
      paymentMethod: paymentMethod,
      subtotal: cart.subtotal,
      deliveryFee: cart.deliveryFee,
      serviceFee: cart.serviceFee,
      tax: cart.tax,
      discount: cart.discount,
      total: cart.total,
      promoCode: cart.promoCode,
      specialInstructions: specialInstructions,
      orderTime: DateTime.now(),
      estimatedDeliveryTime: DateTime.now().add(const Duration(minutes: 30)),
      statusUpdates: [
        OrderStatusUpdate(
          status: OrderStatus.placed,
          timestamp: DateTime.now(),
          message: 'Order placed successfully',
        ),
      ],
    );
  }

  int get totalItems {
    return items.fold(0, (sum, item) => sum + item.quantity);
  }

  // Aliases for compatibility
  DateTime get orderDate => orderTime;
  String get restaurantImage =>
      ''; // This should be fetched from restaurant data

  String get statusText {
    switch (status) {
      case OrderStatus.placed:
        return 'Order Placed';
      case OrderStatus.confirmed:
        return 'Restaurant Confirmed';
      case OrderStatus.preparing:
        return 'Preparing';
      case OrderStatus.ready:
        return 'Ready for Pickup';
      case OrderStatus.pickedUp:
        return 'Picked Up';
      case OrderStatus.onTheWay:
        return 'On the Way';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get paymentMethodText {
    switch (paymentMethod) {
      case PaymentMethod.creditCard:
        return 'Credit Card';
      case PaymentMethod.debitCard:
        return 'Debit Card';
      case PaymentMethod.upi:
        return 'UPI';
      case PaymentMethod.wallet:
        return 'Wallet';
      case PaymentMethod.cashOnDelivery:
        return 'Cash on Delivery';
    }
  }

  bool get canBeCancelled {
    return status == OrderStatus.placed || status == OrderStatus.confirmed;
  }

  bool get canBeRated {
    return status == OrderStatus.delivered && rating == null;
  }

  bool get isActive {
    return status != OrderStatus.delivered && status != OrderStatus.cancelled;
  }

  Order copyWith({
    String? id,
    String? userId,
    String? restaurantId,
    String? restaurantName,
    List<CartItem>? items,
    Address? deliveryAddress,
    OrderStatus? status,
    PaymentMethod? paymentMethod,
    double? subtotal,
    double? deliveryFee,
    double? serviceFee,
    double? tax,
    double? discount,
    double? total,
    String? promoCode,
    String? specialInstructions,
    DateTime? orderTime,
    DateTime? estimatedDeliveryTime,
    DateTime? actualDeliveryTime,
    DeliveryBoy? deliveryBoy,
    List<OrderStatusUpdate>? statusUpdates,
    OrderRating? rating,
  }) {
    return Order(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      restaurantId: restaurantId ?? this.restaurantId,
      restaurantName: restaurantName ?? this.restaurantName,
      items: items ?? this.items,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      status: status ?? this.status,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      subtotal: subtotal ?? this.subtotal,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      serviceFee: serviceFee ?? this.serviceFee,
      tax: tax ?? this.tax,
      discount: discount ?? this.discount,
      total: total ?? this.total,
      promoCode: promoCode ?? this.promoCode,
      specialInstructions: specialInstructions ?? this.specialInstructions,
      orderTime: orderTime ?? this.orderTime,
      estimatedDeliveryTime:
          estimatedDeliveryTime ?? this.estimatedDeliveryTime,
      actualDeliveryTime: actualDeliveryTime ?? this.actualDeliveryTime,
      deliveryBoy: deliveryBoy ?? this.deliveryBoy,
      statusUpdates: statusUpdates ?? this.statusUpdates,
      rating: rating ?? this.rating,
    );
  }

  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['id'],
      userId: json['user_id'],
      restaurantId: json['restaurant_id'],
      restaurantName: json['restaurant_name'],
      items: (json['items'] as List<dynamic>)
          .map((item) => CartItem.fromJson(item))
          .toList(),
      deliveryAddress: Address.fromJson(json['delivery_address']),
      status: OrderStatus.values.firstWhere(
        (status) => status.toString().split('.').last == json['status'],
      ),
      paymentMethod: PaymentMethod.values.firstWhere(
        (method) => method.toString().split('.').last == json['payment_method'],
      ),
      subtotal: json['subtotal'].toDouble(),
      deliveryFee: json['delivery_fee'].toDouble(),
      serviceFee: json['service_fee'].toDouble(),
      tax: json['tax'].toDouble(),
      discount: json['discount'].toDouble(),
      total: json['total'].toDouble(),
      promoCode: json['promo_code'],
      specialInstructions: json['special_instructions'],
      orderTime: DateTime.parse(json['order_time']),
      estimatedDeliveryTime: json['estimated_delivery_time'] != null
          ? DateTime.parse(json['estimated_delivery_time'])
          : null,
      actualDeliveryTime: json['actual_delivery_time'] != null
          ? DateTime.parse(json['actual_delivery_time'])
          : null,
      deliveryBoy: json['delivery_boy'] != null
          ? DeliveryBoy.fromJson(json['delivery_boy'])
          : null,
      statusUpdates: (json['status_updates'] as List<dynamic>?)
              ?.map((update) => OrderStatusUpdate.fromJson(update))
              .toList() ??
          [],
      rating:
          json['rating'] != null ? OrderRating.fromJson(json['rating']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'restaurant_id': restaurantId,
      'restaurant_name': restaurantName,
      'items': items.map((item) => item.toJson()).toList(),
      'delivery_address': deliveryAddress.toJson(),
      'status': status.toString().split('.').last,
      'payment_method': paymentMethod.toString().split('.').last,
      'subtotal': subtotal,
      'delivery_fee': deliveryFee,
      'service_fee': serviceFee,
      'tax': tax,
      'discount': discount,
      'total': total,
      'promo_code': promoCode,
      'special_instructions': specialInstructions,
      'order_time': orderTime.toIso8601String(),
      'estimated_delivery_time': estimatedDeliveryTime?.toIso8601String(),
      'actual_delivery_time': actualDeliveryTime?.toIso8601String(),
      'delivery_boy': deliveryBoy?.toJson(),
      'status_updates': statusUpdates.map((update) => update.toJson()).toList(),
      'rating': rating?.toJson(),
    };
  }
}

class DeliveryBoy {
  final String id;
  final String name;
  final String phone;
  final String? profileImage;
  final double rating;
  final double? currentLatitude;
  final double? currentLongitude;

  DeliveryBoy({
    required this.id,
    required this.name,
    required this.phone,
    this.profileImage,
    required this.rating,
    this.currentLatitude,
    this.currentLongitude,
  });

  factory DeliveryBoy.fromJson(Map<String, dynamic> json) {
    return DeliveryBoy(
      id: json['id'],
      name: json['name'],
      phone: json['phone'],
      profileImage: json['profile_image'],
      rating: json['rating'].toDouble(),
      currentLatitude: json['current_latitude']?.toDouble(),
      currentLongitude: json['current_longitude']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'profile_image': profileImage,
      'rating': rating,
      'current_latitude': currentLatitude,
      'current_longitude': currentLongitude,
    };
  }
}

class OrderStatusUpdate {
  final OrderStatus status;
  final DateTime timestamp;
  final String message;

  OrderStatusUpdate({
    required this.status,
    required this.timestamp,
    required this.message,
  });

  factory OrderStatusUpdate.fromJson(Map<String, dynamic> json) {
    return OrderStatusUpdate(
      status: OrderStatus.values.firstWhere(
        (status) => status.toString().split('.').last == json['status'],
      ),
      timestamp: DateTime.parse(json['timestamp']),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status.toString().split('.').last,
      'timestamp': timestamp.toIso8601String(),
      'message': message,
    };
  }
}

class OrderRating {
  final double restaurantRating;
  final double deliveryRating;
  final String? comment;
  final DateTime ratedAt;

  OrderRating({
    required this.restaurantRating,
    required this.deliveryRating,
    this.comment,
    required this.ratedAt,
  });

  factory OrderRating.fromJson(Map<String, dynamic> json) {
    return OrderRating(
      restaurantRating: json['restaurant_rating'].toDouble(),
      deliveryRating: json['delivery_rating'].toDouble(),
      comment: json['comment'],
      ratedAt: DateTime.parse(json['rated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'restaurant_rating': restaurantRating,
      'delivery_rating': deliveryRating,
      'comment': comment,
      'rated_at': ratedAt.toIso8601String(),
    };
  }
}

import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import '../models/restaurant.dart';
import '../models/cart.dart';
import '../providers/cart_provider.dart';
import '../utils/constants.dart';

class MenuItemCard extends StatelessWidget {
  final MenuItem menuItem;
  final Restaurant restaurant;

  const MenuItemCard({
    super.key,
    required this.menuItem,
    required this.restaurant,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Item Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name and Indicators
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          menuItem.name,
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                      ),
                      // Veg/Non-veg indicator
                      Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: menuItem.isVegetarian
                                ? Colors.green
                                : Colors.red,
                            width: 2,
                          ),
                        ),
                        child: Center(
                          child: Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: menuItem.isVegetarian
                                  ? Colors.green
                                  : Colors.red,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 4),

                  // Description
                  if (menuItem.description.isNotEmpty)
                    Text(
                      menuItem.description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                  const SizedBox(height: AppDimensions.marginS),

                  // Tags
                  Row(
                    children: [
                      if (menuItem.isPopular)
                        _buildTag('Popular', AppColors.warning),
                      if (menuItem.isSpicy) _buildTag('Spicy', AppColors.error),
                      if (menuItem.isVegan)
                        _buildTag('Vegan', AppColors.success),
                    ],
                  ),

                  const SizedBox(height: AppDimensions.marginS),

                  // Price and Add Button
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '\$${menuItem.price.toStringAsFixed(2)}',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primary,
                                ),
                      ),
                      Consumer<CartProvider>(
                        builder: (context, cartProvider, child) {
                          final cartItem =
                              cartProvider.getItemByMenuItemId(menuItem.id);

                          if (cartItem != null) {
                            return _buildQuantityControls(context, cartItem);
                          }

                          return ElevatedButton(
                            onPressed: menuItem.isAvailable
                                ? () => _addToCart(context)
                                : null,
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppDimensions.paddingM,
                                vertical: AppDimensions.paddingS,
                              ),
                            ),
                            child: Text(
                              menuItem.isAvailable ? 'Add' : 'Unavailable',
                              style:
                                  const TextStyle(fontWeight: FontWeight.w600),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(width: AppDimensions.marginM),

            // Item Image
            if (menuItem.imageUrl.isNotEmpty)
              ClipRRect(
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                child: CachedNetworkImage(
                  imageUrl: menuItem.imageUrl,
                  width: 80,
                  height: 80,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    width: 80,
                    height: 80,
                    color: AppColors.background,
                    child: const Icon(
                      Icons.fastfood,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    width: 80,
                    height: 80,
                    color: AppColors.background,
                    child: const Icon(
                      Icons.fastfood,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTag(String text, Color color) {
    return Container(
      margin: const EdgeInsets.only(right: AppDimensions.marginS),
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingS,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        border: Border.all(color: color, width: 1),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildQuantityControls(BuildContext context, CartItem cartItem) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Decrease Button
        InkWell(
          onTap: () {
            if (cartItem.quantity > 1) {
              Provider.of<CartProvider>(context, listen: false)
                  .updateQuantity(cartItem.id, cartItem.quantity - 1);
            } else {
              Provider.of<CartProvider>(context, listen: false)
                  .removeItem(cartItem.id);
            }
          },
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.primary),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: Icon(
              cartItem.quantity > 1 ? Icons.remove : Icons.delete_outline,
              size: 18,
              color:
                  cartItem.quantity > 1 ? AppColors.primary : AppColors.error,
            ),
          ),
        ),

        // Quantity
        Container(
          width: 40,
          alignment: Alignment.center,
          child: Text(
            '${cartItem.quantity}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
        ),

        // Increase Button
        InkWell(
          onTap: () {
            Provider.of<CartProvider>(context, listen: false)
                .updateQuantity(cartItem.id, cartItem.quantity + 1);
          },
          borderRadius: BorderRadius.circular(AppDimensions.radiusS),
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: const Icon(
              Icons.add,
              size: 18,
              color: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  void _addToCart(BuildContext context) {
    final cartItem = CartItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      menuItemId: menuItem.id,
      restaurantId: restaurant.id,
      restaurantName: restaurant.name,
      name: menuItem.name,
      description: menuItem.description,
      basePrice: menuItem.price,
      quantity: 1,
      imageUrl: menuItem.imageUrl,
      selectedCustomizations: [], // Add customization logic if needed
    );

    Provider.of<CartProvider>(context, listen: false).addCartItem(cartItem);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${menuItem.name} added to cart'),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 2),
        action: SnackBarAction(
          label: 'View Cart',
          textColor: Colors.white,
          onPressed: () {
            // Navigate to cart
          },
        ),
      ),
    );
  }
}

import 'package:equatable/equatable.dart';
import '../../models/order.dart';
import '../../models/cart.dart';

abstract class OrderEvent extends Equatable {
  const OrderEvent();

  @override
  List<Object?> get props => [];
}

class OrderStarted extends OrderEvent {}

class OrderPlaceRequested extends OrderEvent {
  final List<CartItem> items;
  final double total;
  final String deliveryAddress;
  final String paymentMethod;
  final String? specialInstructions;
  final String? promoCode;

  const OrderPlaceRequested({
    required this.items,
    required this.total,
    required this.deliveryAddress,
    required this.paymentMethod,
    this.specialInstructions,
    this.promoCode,
  });

  @override
  List<Object?> get props => [
        items,
        total,
        deliveryAddress,
        paymentMethod,
        specialInstructions,
        promoCode,
      ];
}

class OrderHistoryRequested extends OrderEvent {}

class OrderDetailsRequested extends OrderEvent {
  final String orderId;

  const OrderDetailsRequested({required this.orderId});

  @override
  List<Object> get props => [orderId];
}

class OrderTrackingRequested extends OrderEvent {
  final String orderId;

  const OrderTrackingRequested({required this.orderId});

  @override
  List<Object> get props => [orderId];
}

class OrderCancelRequested extends OrderEvent {
  final String orderId;
  final String reason;

  const OrderCancelRequested({
    required this.orderId,
    required this.reason,
  });

  @override
  List<Object> get props => [orderId, reason];
}

class OrderRateRequested extends OrderEvent {
  final String orderId;
  final double rating;
  final String? review;

  const OrderRateRequested({
    required this.orderId,
    required this.rating,
    this.review,
  });

  @override
  List<Object?> get props => [orderId, rating, review];
}

class OrderReorderRequested extends OrderEvent {
  final String orderId;

  const OrderReorderRequested({required this.orderId});

  @override
  List<Object> get props => [orderId];
}

class OrderStatusUpdated extends OrderEvent {
  final String orderId;
  final OrderStatus status;

  const OrderStatusUpdated({
    required this.orderId,
    required this.status,
  });

  @override
  List<Object> get props => [orderId, status];
}

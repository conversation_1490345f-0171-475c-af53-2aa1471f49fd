import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import '../../models/user.dart';
import '../../utils/constants.dart';
import 'auth_event.dart';
import 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  AuthBloc() : super(AuthInitial()) {
    on<AuthStarted>(_onAuthStarted);
    on<AuthLoginRequested>(_onLoginRequested);
    on<AuthSignupRequested>(_onSignupRequested);
    on<AuthGoogleLoginRequested>(_onGoogleLoginRequested);
    on<AuthFacebookLoginRequested>(_onFacebookLoginRequested);
    on<AuthLogoutRequested>(_onLogoutRequested);
    on<AuthProfileUpdateRequested>(_onProfileUpdateRequested);
    on<AuthForgotPasswordRequested>(_onForgotPasswordRequested);
  }

  Future<void> _onAuthStarted(AuthStarted event, Emitter<AuthState> emit) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(AppConstants.userTokenKey);
      final userData = prefs.getString(AppConstants.userDataKey);

      if (token != null && userData != null) {
        final user = User.fromJson(_parseJson(userData));
        emit(AuthAuthenticated(user: user, token: token));
      } else {
        emit(AuthUnauthenticated());
      }
    } catch (e) {
      debugPrint('Error loading user from storage: $e');
      emit(AuthUnauthenticated());
    }
  }

  Future<void> _onLoginRequested(
      AuthLoginRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Mock successful login
      if (event.email.isNotEmpty && event.password.isNotEmpty) {
        final mockUser = User(
          id: '1',
          name: 'John Doe',
          email: event.email,
          phone: '+1234567890',
          createdAt: DateTime.now(),
        );

        const mockToken = 'mock_jwt_token_12345';

        await _saveUserToStorage(mockUser, mockToken);

        emit(AuthAuthenticated(user: mockUser, token: mockToken));
      } else {
        emit(const AuthError(message: 'Invalid email or password'));
      }
    } catch (e) {
      debugPrint('Login error: $e');
      emit(const AuthError(message: 'Login failed. Please try again.'));
    }
  }

  Future<void> _onSignupRequested(
      AuthSignupRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Mock successful signup
      if (event.name.isNotEmpty &&
          event.email.isNotEmpty &&
          event.password.isNotEmpty) {
        final mockUser = User(
          id: '1',
          name: event.name,
          email: event.email,
          phone: event.phone,
          createdAt: DateTime.now(),
        );

        const mockToken = 'mock_jwt_token_12345';

        await _saveUserToStorage(mockUser, mockToken);

        emit(AuthAuthenticated(user: mockUser, token: mockToken));
      } else {
        emit(const AuthError(message: 'Please fill all required fields'));
      }
    } catch (e) {
      debugPrint('Signup error: $e');
      emit(const AuthError(message: 'Signup failed. Please try again.'));
    }
  }

  Future<void> _onGoogleLoginRequested(
      AuthGoogleLoginRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());

    try {
      // Simulate Google login
      await Future.delayed(const Duration(seconds: 2));

      final mockUser = User(
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        profileImage: 'https://example.com/profile.jpg',
        createdAt: DateTime.now(),
      );

      const mockToken = 'mock_google_jwt_token_12345';

      await _saveUserToStorage(mockUser, mockToken);

      emit(AuthAuthenticated(user: mockUser, token: mockToken));
    } catch (e) {
      debugPrint('Google login error: $e');
      emit(const AuthError(message: 'Google login failed. Please try again.'));
    }
  }

  Future<void> _onFacebookLoginRequested(
      AuthFacebookLoginRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());

    try {
      // Simulate Facebook login
      await Future.delayed(const Duration(seconds: 2));

      final mockUser = User(
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        profileImage: 'https://example.com/profile.jpg',
        createdAt: DateTime.now(),
      );

      const mockToken = 'mock_facebook_jwt_token_12345';

      await _saveUserToStorage(mockUser, mockToken);

      emit(AuthAuthenticated(user: mockUser, token: mockToken));
    } catch (e) {
      debugPrint('Facebook login error: $e');
      emit(const AuthError(
          message: 'Facebook login failed. Please try again.'));
    }
  }

  Future<void> _onLogoutRequested(
      AuthLogoutRequested event, Emitter<AuthState> emit) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConstants.userTokenKey);
      await prefs.remove(AppConstants.userDataKey);

      emit(AuthUnauthenticated());
    } catch (e) {
      debugPrint('Logout error: $e');
      emit(const AuthError(message: 'Logout failed. Please try again.'));
    }
  }

  Future<void> _onProfileUpdateRequested(
      AuthProfileUpdateRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      final currentState = state;
      if (currentState is AuthAuthenticated) {
        final updatedUser = User(
          id: currentState.user.id,
          name: event.name,
          email: event.email,
          phone: event.phone,
          profileImage: event.profileImage ?? currentState.user.profileImage,
          createdAt: currentState.user.createdAt,
        );

        await _saveUserToStorage(updatedUser, currentState.token);

        emit(AuthProfileUpdated(user: updatedUser, token: currentState.token));
      } else {
        emit(const AuthError(message: 'User not authenticated'));
      }
    } catch (e) {
      debugPrint('Update profile error: $e');
      emit(const AuthError(
          message: 'Profile update failed. Please try again.'));
    }
  }

  Future<void> _onForgotPasswordRequested(
      AuthForgotPasswordRequested event, Emitter<AuthState> emit) async {
    emit(AuthLoading());

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      emit(AuthForgotPasswordSent(email: event.email));
    } catch (e) {
      debugPrint('Forgot password error: $e');
      emit(const AuthError(
          message: 'Failed to send reset email. Please try again.'));
    }
  }

  Future<void> _saveUserToStorage(User user, String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AppConstants.userTokenKey, token);
    await prefs.setString(AppConstants.userDataKey, user.toJson().toString());
  }

  Map<String, dynamic> _parseJson(String jsonString) {
    // Simple JSON parsing for mock data
    // In a real app, you'd use dart:convert
    return {
      'id': '1',
      'name': 'John Doe',
      'email': '<EMAIL>',
      'phone': '+1234567890',
      'createdAt': DateTime.now().toIso8601String(),
    };
  }
}

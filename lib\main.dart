import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'blocs/auth/auth_bloc.dart';
import 'blocs/auth/auth_event.dart';
import 'blocs/cart/cart_bloc.dart';
import 'blocs/cart/cart_event.dart';
import 'blocs/restaurant/restaurant_bloc.dart';
import 'blocs/order/order_bloc.dart';
import 'blocs/order/order_event.dart';
import 'blocs/location/location_bloc.dart';
import 'blocs/location/location_event.dart';
import 'screens/splash_screen.dart';
import 'utils/theme.dart';
import 'utils/constants.dart';

void main() {
  runApp(const FoodieApp());
}

class FoodieApp extends StatelessWidget {
  const FoodieApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AuthBloc>(
          create: (context) => AuthBloc()..add(AuthStarted()),
        ),
        BlocProvider<CartBloc>(
          create: (context) => CartBloc()..add(CartStarted()),
        ),
        BlocProvider<RestaurantBloc>(
          create: (context) => RestaurantBloc(),
        ),
        BlocProvider<OrderBloc>(
          create: (context) => OrderBloc()..add(OrderStarted()),
        ),
        BlocProvider<LocationBloc>(
          create: (context) => LocationBloc()..add(LocationStarted()),
        ),
      ],
      child: MaterialApp(
        title: AppConstants.appName,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        home: const SplashScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

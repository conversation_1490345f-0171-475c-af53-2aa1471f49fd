import 'package:equatable/equatable.dart';
import '../../models/restaurant.dart';

abstract class RestaurantState extends Equatable {
  const RestaurantState();

  @override
  List<Object?> get props => [];
}

class RestaurantInitial extends RestaurantState {}

class RestaurantLoading extends RestaurantState {}

class RestaurantLoaded extends RestaurantState {
  final List<Restaurant> restaurants;
  final List<Restaurant> filteredRestaurants;
  final String? selectedCategory;
  final String? searchQuery;
  final Map<String, dynamic>? appliedFilters;

  const RestaurantLoaded({
    required this.restaurants,
    required this.filteredRestaurants,
    this.selectedCategory,
    this.searchQuery,
    this.appliedFilters,
  });

  @override
  List<Object?> get props => [
        restaurants,
        filteredRestaurants,
        selectedCategory,
        searchQuery,
        appliedFilters,
      ];

  RestaurantLoaded copyWith({
    List<Restaurant>? restaurants,
    List<Restaurant>? filteredRestaurants,
    String? selectedCategory,
    String? searchQuery,
    Map<String, dynamic>? appliedFilters,
  }) {
    return RestaurantLoaded(
      restaurants: restaurants ?? this.restaurants,
      filteredRestaurants: filteredRestaurants ?? this.filteredRestaurants,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      searchQuery: searchQuery ?? this.searchQuery,
      appliedFilters: appliedFilters ?? this.appliedFilters,
    );
  }
}

class RestaurantDetailsLoaded extends RestaurantState {
  final Restaurant restaurant;

  const RestaurantDetailsLoaded({required this.restaurant});

  @override
  List<Object> get props => [restaurant];
}

class RestaurantError extends RestaurantState {
  final String message;

  const RestaurantError({required this.message});

  @override
  List<Object> get props => [message];
}

class RestaurantSearchResults extends RestaurantState {
  final List<Restaurant> results;
  final String query;

  const RestaurantSearchResults({
    required this.results,
    required this.query,
  });

  @override
  List<Object> get props => [results, query];
}

class RestaurantFilterResults extends RestaurantState {
  final List<Restaurant> results;
  final Map<String, dynamic> filters;

  const RestaurantFilterResults({
    required this.results,
    required this.filters,
  });

  @override
  List<Object> get props => [results, filters];
}

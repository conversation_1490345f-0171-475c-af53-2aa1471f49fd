class Restaurant {
  final String id;
  final String name;
  final String description;
  final String cuisine;
  final String imageUrl;
  final List<String> imageGallery;
  final double rating;
  final int reviewCount;
  final String address;
  final double latitude;
  final double longitude;
  final String phone;
  final String email;
  final int estimatedDeliveryTime; // in minutes
  final double minimumOrderValue;
  final double deliveryFee;
  final bool isOpen;
  final String openingHours;
  final String closingHours;
  final List<String> tags; // e.g., "Fast Food", "Vegetarian", "Popular"
  final double? discountPercentage;
  final List<MenuCategory> menuCategories;
  final DateTime createdAt;

  Restaurant({
    required this.id,
    required this.name,
    required this.description,
    required this.cuisine,
    required this.imageUrl,
    this.imageGallery = const [],
    required this.rating,
    required this.reviewCount,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.phone,
    required this.email,
    required this.estimatedDeliveryTime,
    required this.minimumOrderValue,
    required this.deliveryFee,
    required this.isOpen,
    required this.openingHours,
    required this.closingHours,
    this.tags = const [],
    this.discountPercentage,
    this.menuCategories = const [],
    required this.createdAt,
  });

  factory Restaurant.fromJson(Map<String, dynamic> json) {
    return Restaurant(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      cuisine: json['cuisine'],
      imageUrl: json['image_url'],
      imageGallery: List<String>.from(json['image_gallery'] ?? []),
      rating: json['rating'].toDouble(),
      reviewCount: json['review_count'],
      address: json['address'],
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
      phone: json['phone'],
      email: json['email'],
      estimatedDeliveryTime: json['estimated_delivery_time'],
      minimumOrderValue: json['minimum_order_value'].toDouble(),
      deliveryFee: json['delivery_fee'].toDouble(),
      isOpen: json['is_open'],
      openingHours: json['opening_hours'],
      closingHours: json['closing_hours'],
      tags: List<String>.from(json['tags'] ?? []),
      discountPercentage: json['discount_percentage']?.toDouble(),
      menuCategories: (json['menu_categories'] as List<dynamic>?)
              ?.map((category) => MenuCategory.fromJson(category))
              .toList() ??
          [],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'cuisine': cuisine,
      'image_url': imageUrl,
      'image_gallery': imageGallery,
      'rating': rating,
      'review_count': reviewCount,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'phone': phone,
      'email': email,
      'estimated_delivery_time': estimatedDeliveryTime,
      'minimum_order_value': minimumOrderValue,
      'delivery_fee': deliveryFee,
      'is_open': isOpen,
      'opening_hours': openingHours,
      'closing_hours': closingHours,
      'tags': tags,
      'discount_percentage': discountPercentage,
      'menu_categories': menuCategories.map((category) => category.toJson()).toList(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  String get deliveryTimeText => '$estimatedDeliveryTime mins';
  
  String get ratingText => rating.toStringAsFixed(1);
  
  bool get hasDiscount => discountPercentage != null && discountPercentage! > 0;
}

class MenuCategory {
  final String id;
  final String name;
  final String description;
  final List<MenuItem> items;

  MenuCategory({
    required this.id,
    required this.name,
    required this.description,
    this.items = const [],
  });

  factory MenuCategory.fromJson(Map<String, dynamic> json) {
    return MenuCategory(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      items: (json['items'] as List<dynamic>?)
              ?.map((item) => MenuItem.fromJson(item))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'items': items.map((item) => item.toJson()).toList(),
    };
  }
}

class MenuItem {
  final String id;
  final String name;
  final String description;
  final double price;
  final String imageUrl;
  final bool isVegetarian;
  final bool isVegan;
  final bool isSpicy;
  final bool isPopular;
  final bool isAvailable;
  final List<MenuItemCustomization> customizations;
  final int calories;
  final List<String> allergens;

  MenuItem({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.imageUrl,
    this.isVegetarian = false,
    this.isVegan = false,
    this.isSpicy = false,
    this.isPopular = false,
    this.isAvailable = true,
    this.customizations = const [],
    this.calories = 0,
    this.allergens = const [],
  });

  factory MenuItem.fromJson(Map<String, dynamic> json) {
    return MenuItem(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      price: json['price'].toDouble(),
      imageUrl: json['image_url'],
      isVegetarian: json['is_vegetarian'] ?? false,
      isVegan: json['is_vegan'] ?? false,
      isSpicy: json['is_spicy'] ?? false,
      isPopular: json['is_popular'] ?? false,
      isAvailable: json['is_available'] ?? true,
      customizations: (json['customizations'] as List<dynamic>?)
              ?.map((customization) => MenuItemCustomization.fromJson(customization))
              .toList() ??
          [],
      calories: json['calories'] ?? 0,
      allergens: List<String>.from(json['allergens'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'image_url': imageUrl,
      'is_vegetarian': isVegetarian,
      'is_vegan': isVegan,
      'is_spicy': isSpicy,
      'is_popular': isPopular,
      'is_available': isAvailable,
      'customizations': customizations.map((customization) => customization.toJson()).toList(),
      'calories': calories,
      'allergens': allergens,
    };
  }

  String get priceText => '\$${price.toStringAsFixed(2)}';
}

class MenuItemCustomization {
  final String id;
  final String name;
  final String type; // 'size', 'topping', 'addon'
  final List<CustomizationOption> options;
  final bool isRequired;
  final int maxSelections;

  MenuItemCustomization({
    required this.id,
    required this.name,
    required this.type,
    this.options = const [],
    this.isRequired = false,
    this.maxSelections = 1,
  });

  factory MenuItemCustomization.fromJson(Map<String, dynamic> json) {
    return MenuItemCustomization(
      id: json['id'],
      name: json['name'],
      type: json['type'],
      options: (json['options'] as List<dynamic>?)
              ?.map((option) => CustomizationOption.fromJson(option))
              .toList() ??
          [],
      isRequired: json['is_required'] ?? false,
      maxSelections: json['max_selections'] ?? 1,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'options': options.map((option) => option.toJson()).toList(),
      'is_required': isRequired,
      'max_selections': maxSelections,
    };
  }
}

class CustomizationOption {
  final String id;
  final String name;
  final double additionalPrice;

  CustomizationOption({
    required this.id,
    required this.name,
    this.additionalPrice = 0.0,
  });

  factory CustomizationOption.fromJson(Map<String, dynamic> json) {
    return CustomizationOption(
      id: json['id'],
      name: json['name'],
      additionalPrice: json['additional_price']?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'additional_price': additionalPrice,
    };
  }

  String get priceText => additionalPrice > 0 ? '+\$${additionalPrice.toStringAsFixed(2)}' : '';
}

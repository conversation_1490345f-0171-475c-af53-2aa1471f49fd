import 'package:equatable/equatable.dart';

abstract class LocationEvent extends Equatable {
  const LocationEvent();

  @override
  List<Object?> get props => [];
}

class LocationStarted extends LocationEvent {}

class LocationPermissionRequested extends LocationEvent {}

class LocationCurrentRequested extends LocationEvent {}

class LocationAddressSelected extends LocationEvent {
  final String address;
  final double latitude;
  final double longitude;

  const LocationAddressSelected({
    required this.address,
    required this.latitude,
    required this.longitude,
  });

  @override
  List<Object> get props => [address, latitude, longitude];
}

class LocationAddressAdded extends LocationEvent {
  final SavedAddress address;

  const LocationAddressAdded({required this.address});

  @override
  List<Object> get props => [address];
}

class LocationAddressUpdated extends LocationEvent {
  final SavedAddress address;

  const LocationAddressUpdated({required this.address});

  @override
  List<Object> get props => [address];
}

class LocationAddressDeleted extends LocationEvent {
  final String addressId;

  const LocationAddressDeleted({required this.addressId});

  @override
  List<Object> get props => [addressId];
}

class LocationSavedAddressesRequested extends LocationEvent {}

class LocationAddressSearchRequested extends LocationEvent {
  final String query;

  const LocationAddressSearchRequested({required this.query});

  @override
  List<Object> get props => [query];
}

class LocationMapPositionChanged extends LocationEvent {
  final double latitude;
  final double longitude;

  const LocationMapPositionChanged({
    required this.latitude,
    required this.longitude,
  });

  @override
  List<Object> get props => [latitude, longitude];
}

// Model for saved addresses
class SavedAddress {
  final String id;
  final String label; // Home, Work, Other
  final String address;
  final String? landmark;
  final double latitude;
  final double longitude;
  final bool isDefault;

  const SavedAddress({
    required this.id,
    required this.label,
    required this.address,
    this.landmark,
    required this.latitude,
    required this.longitude,
    this.isDefault = false,
  });

  SavedAddress copyWith({
    String? id,
    String? label,
    String? address,
    String? landmark,
    double? latitude,
    double? longitude,
    bool? isDefault,
  }) {
    return SavedAddress(
      id: id ?? this.id,
      label: label ?? this.label,
      address: address ?? this.address,
      landmark: landmark ?? this.landmark,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      isDefault: isDefault ?? this.isDefault,
    );
  }
}

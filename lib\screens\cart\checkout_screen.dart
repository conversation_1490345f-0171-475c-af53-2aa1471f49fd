import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/cart_provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/order_provider.dart';
import '../../models/order.dart';
import '../../utils/constants.dart';
import '../orders/order_tracking_screen.dart';

class CheckoutScreen extends StatefulWidget {
  const CheckoutScreen({super.key});

  @override
  State<CheckoutScreen> createState() => _CheckoutScreenState();
}

class _CheckoutScreenState extends State<CheckoutScreen> {
  String _selectedPaymentMethod = 'Credit Card';
  final _instructionsController = TextEditingController();

  final List<String> _paymentMethods = [
    'Credit Card',
    'Debit Card',
    'UPI',
    'Wallet',
    'Cash on Delivery',
  ];

  @override
  void dispose() {
    _instructionsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Checkout'),
        backgroundColor: Colors.white,
        foregroundColor: AppColors.textPrimary,
        elevation: 0,
      ),
      body: Consumer3<CartProvider, AuthProvider, OrderProvider>(
        builder: (context, cartProvider, authProvider, orderProvider, child) {
          return Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(AppDimensions.paddingM),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Delivery Address
                      _buildSectionHeader('Delivery Address'),
                      _buildAddressCard(authProvider.user?.addresses.first),

                      const SizedBox(height: AppDimensions.marginL),

                      // Order Summary
                      _buildSectionHeader('Order Summary'),
                      _buildOrderSummary(cartProvider),

                      const SizedBox(height: AppDimensions.marginL),

                      // Payment Method
                      _buildSectionHeader('Payment Method'),
                      _buildPaymentMethods(),

                      const SizedBox(height: AppDimensions.marginL),

                      // Special Instructions
                      _buildSectionHeader('Special Instructions'),
                      TextField(
                        controller: _instructionsController,
                        decoration: const InputDecoration(
                          hintText:
                              'Add cooking instructions, delivery notes, etc.',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
              ),

              // Place Order Button
              Container(
                padding: const EdgeInsets.all(AppDimensions.paddingM),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 4,
                      offset: Offset(0, -2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Total Amount',
                          style:
                              Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                        Text(
                          '\$${cartProvider.total.toStringAsFixed(2)}',
                          style:
                              Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.primary,
                                  ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppDimensions.marginM),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: orderProvider.isLoading
                            ? null
                            : () => _placeOrder(context, cartProvider,
                                authProvider, orderProvider),
                        child: orderProvider.isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : const Text(
                                'Place Order',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppDimensions.marginM),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
      ),
    );
  }

  Widget _buildAddressCard(dynamic address) {
    if (address == null) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Row(
            children: [
              Icon(
                Icons.add_location,
                color: AppColors.primary,
              ),
              const SizedBox(width: AppDimensions.marginM),
              const Expanded(
                child: Text('Add delivery address'),
              ),
              Icon(
                Icons.chevron_right,
                color: AppColors.textSecondary,
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  color: AppColors.primary,
                  size: 20,
                ),
                const SizedBox(width: AppDimensions.marginS),
                Text(
                  address.label,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Change address
                  },
                  child: const Text('Change'),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              '${address.addressLine1}, ${address.city}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textSecondary,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderSummary(CartProvider cartProvider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          children: [
            ...cartProvider.items.map((item) {
              return Padding(
                padding: const EdgeInsets.only(bottom: AppDimensions.marginS),
                child: Row(
                  children: [
                    Text('${item.quantity}x '),
                    Expanded(child: Text(item.name)),
                    Text(
                        '\$${(item.price * item.quantity).toStringAsFixed(2)}'),
                  ],
                ),
              );
            }).toList(),
            const Divider(),
            _buildSummaryRow('Subtotal', cartProvider.subtotal),
            _buildSummaryRow('Delivery Fee', cartProvider.deliveryFee),
            _buildSummaryRow('Tax', cartProvider.tax),
            const Divider(),
            _buildSummaryRow('Total', cartProvider.total, isTotal: true),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                ),
          ),
          Text(
            '\$${amount.toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                  color: isTotal ? AppColors.primary : null,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethods() {
    return Card(
      child: Column(
        children: _paymentMethods.map((method) {
          return RadioListTile<String>(
            title: Text(method),
            value: method,
            groupValue: _selectedPaymentMethod,
            onChanged: (value) {
              setState(() {
                _selectedPaymentMethod = value!;
              });
            },
            activeColor: AppColors.primary,
          );
        }).toList(),
      ),
    );
  }

  Future<void> _placeOrder(
    BuildContext context,
    CartProvider cartProvider,
    AuthProvider authProvider,
    OrderProvider orderProvider,
  ) async {
    final order = Order(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: authProvider.user!.id,
      restaurantId: cartProvider.items.first.restaurantId,
      restaurantName: cartProvider.items.first.restaurantName,
      items: cartProvider.items,
      subtotal: cartProvider.subtotal,
      deliveryFee: cartProvider.deliveryFee,
      serviceFee: 0.0,
      tax: cartProvider.tax,
      discount: 0.0,
      total: cartProvider.total,
      status: OrderStatus.placed,
      orderTime: DateTime.now(),
      estimatedDeliveryTime: DateTime.now().add(const Duration(minutes: 30)),
      deliveryAddress: authProvider.user!.addresses.first,
      paymentMethod: _getPaymentMethodEnum(_selectedPaymentMethod),
      specialInstructions: _instructionsController.text,
      statusUpdates: [
        OrderStatusUpdate(
          status: OrderStatus.placed,
          timestamp: DateTime.now(),
          message: 'Order placed successfully',
        ),
      ],
    );

    final success = await orderProvider.placeOrder(order);

    if (success && context.mounted) {
      cartProvider.clearCart();

      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(
          builder: (context) => OrderTrackingScreen(order: order),
        ),
        (route) => route.isFirst,
      );
    } else if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to place order. Please try again.'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  PaymentMethod _getPaymentMethodEnum(String method) {
    switch (method) {
      case 'Credit Card':
        return PaymentMethod.creditCard;
      case 'Debit Card':
        return PaymentMethod.debitCard;
      case 'UPI':
        return PaymentMethod.upi;
      case 'Wallet':
        return PaymentMethod.wallet;
      case 'Cash on Delivery':
        return PaymentMethod.cashOnDelivery;
      default:
        return PaymentMethod.creditCard;
    }
  }
}

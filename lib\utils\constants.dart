import 'package:flutter/material.dart';

class AppConstants {
  // App Info
  static const String appName = 'Foodie';
  static const String appVersion = '1.0.0';
  
  // API Configuration
  static const String baseUrl = 'https://api.foodieapp.com';
  static const String apiVersion = 'v1';
  static const String apiUrl = '$baseUrl/$apiVersion';
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String cartDataKey = 'cart_data';
  static const String addressesKey = 'addresses';
  static const String onboardingCompletedKey = 'onboarding_completed';
  
  // Default Values
  static const double defaultDeliveryFee = 2.99;
  static const double defaultServiceFee = 1.99;
  static const double defaultTaxRate = 0.08; // 8%
  static const int defaultDeliveryTime = 30; // minutes
  static const double minimumOrderValue = 10.0;
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 50;
  
  // Location
  static const double defaultLocationRadius = 10.0; // km
  static const double maxLocationRadius = 50.0; // km
  
  // Images
  static const String defaultRestaurantImage = 'assets/images/default_restaurant.png';
  static const String defaultFoodImage = 'assets/images/default_food.png';
  static const String defaultUserImage = 'assets/images/default_user.png';
  static const String appLogo = 'assets/images/app_logo.png';
  
  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  
  // Timeouts
  static const Duration apiTimeout = Duration(seconds: 30);
  static const Duration locationTimeout = Duration(seconds: 10);
}

class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFFFF6B35);
  static const Color primaryDark = Color(0xFFE55A2B);
  static const Color primaryLight = Color(0xFFFF8A65);
  
  // Secondary Colors
  static const Color secondary = Color(0xFF2E7D32);
  static const Color secondaryDark = Color(0xFF1B5E20);
  static const Color secondaryLight = Color(0xFF4CAF50);
  
  // Neutral Colors
  static const Color background = Color(0xFFFAFAFA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF5F5F5);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFFBDBDBD);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  
  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // Special Colors
  static const Color vegetarian = Color(0xFF4CAF50);
  static const Color nonVegetarian = Color(0xFFF44336);
  static const Color spicy = Color(0xFFFF5722);
  static const Color popular = Color(0xFFFF9800);
  static const Color discount = Color(0xFFE91E63);
  
  // Rating Colors
  static const Color ratingGold = Color(0xFFFFD700);
  static const Color ratingGray = Color(0xFFE0E0E0);
  
  // Delivery Status Colors
  static const Color statusPlaced = Color(0xFF2196F3);
  static const Color statusConfirmed = Color(0xFF4CAF50);
  static const Color statusPreparing = Color(0xFFFF9800);
  static const Color statusReady = Color(0xFF9C27B0);
  static const Color statusPickedUp = Color(0xFF673AB7);
  static const Color statusOnTheWay = Color(0xFF3F51B5);
  static const Color statusDelivered = Color(0xFF4CAF50);
  static const Color statusCancelled = Color(0xFFF44336);
}

class AppTextStyles {
  // Headings
  static const TextStyle h1 = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );
  
  static const TextStyle h2 = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );
  
  static const TextStyle h3 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
  );
  
  static const TextStyle h4 = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
  );
  
  static const TextStyle h5 = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w500,
    color: AppColors.textPrimary,
  );
  
  static const TextStyle h6 = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: AppColors.textPrimary,
  );
  
  // Body Text
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.textPrimary,
  );
  
  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.textPrimary,
  );
  
  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.textSecondary,
  );
  
  // Special Text Styles
  static const TextStyle caption = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.textHint,
  );
  
  static const TextStyle button = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: AppColors.textOnPrimary,
  );
  
  static const TextStyle price = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.bold,
    color: AppColors.primary,
  );
  
  static const TextStyle discount = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.bold,
    color: AppColors.discount,
  );
  
  static const TextStyle rating = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: AppColors.textPrimary,
  );
}

class AppDimensions {
  // Padding
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 16.0;
  static const double paddingL = 24.0;
  static const double paddingXL = 32.0;
  
  // Margin
  static const double marginXS = 4.0;
  static const double marginS = 8.0;
  static const double marginM = 16.0;
  static const double marginL = 24.0;
  static const double marginXL = 32.0;
  
  // Border Radius
  static const double radiusXS = 4.0;
  static const double radiusS = 8.0;
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXL = 24.0;
  static const double radiusCircular = 50.0;
  
  // Icon Sizes
  static const double iconXS = 16.0;
  static const double iconS = 20.0;
  static const double iconM = 24.0;
  static const double iconL = 32.0;
  static const double iconXL = 48.0;
  
  // Button Heights
  static const double buttonHeightS = 36.0;
  static const double buttonHeightM = 48.0;
  static const double buttonHeightL = 56.0;
  
  // Card Heights
  static const double cardHeightS = 120.0;
  static const double cardHeightM = 200.0;
  static const double cardHeightL = 280.0;
  
  // App Bar
  static const double appBarHeight = 56.0;
  static const double appBarElevation = 2.0;
  
  // Bottom Navigation
  static const double bottomNavHeight = 60.0;
  
  // List Item Heights
  static const double listItemHeightS = 48.0;
  static const double listItemHeightM = 64.0;
  static const double listItemHeightL = 80.0;
}

class AppStrings {
  // App
  static const String appName = 'Foodie';
  static const String appTagline = 'Delicious food delivered to your door';
  
  // Onboarding
  static const String onboardingTitle1 = 'Discover Great Food';
  static const String onboardingSubtitle1 = 'Find the best restaurants and dishes near you';
  static const String onboardingTitle2 = 'Fast Delivery';
  static const String onboardingSubtitle2 = 'Get your favorite food delivered quickly';
  static const String onboardingTitle3 = 'Easy Payment';
  static const String onboardingSubtitle3 = 'Pay with your preferred method securely';
  
  // Authentication
  static const String login = 'Login';
  static const String signup = 'Sign Up';
  static const String email = 'Email';
  static const String password = 'Password';
  static const String confirmPassword = 'Confirm Password';
  static const String forgotPassword = 'Forgot Password?';
  static const String dontHaveAccount = "Don't have an account?";
  static const String alreadyHaveAccount = 'Already have an account?';
  
  // Navigation
  static const String home = 'Home';
  static const String search = 'Search';
  static const String orders = 'Orders';
  static const String profile = 'Profile';
  
  // Common
  static const String loading = 'Loading...';
  static const String error = 'Error';
  static const String retry = 'Retry';
  static const String cancel = 'Cancel';
  static const String confirm = 'Confirm';
  static const String save = 'Save';
  static const String edit = 'Edit';
  static const String delete = 'Delete';
  static const String add = 'Add';
  static const String remove = 'Remove';
  static const String next = 'Next';
  static const String previous = 'Previous';
  static const String done = 'Done';
  static const String skip = 'Skip';
  static const String getStarted = 'Get Started';
  
  // Restaurant
  static const String restaurants = 'Restaurants';
  static const String menu = 'Menu';
  static const String reviews = 'Reviews';
  static const String info = 'Info';
  static const String openNow = 'Open Now';
  static const String closed = 'Closed';
  static const String deliveryTime = 'Delivery Time';
  static const String minimumOrder = 'Minimum Order';
  static const String deliveryFee = 'Delivery Fee';
  
  // Cart & Checkout
  static const String cart = 'Cart';
  static const String checkout = 'Checkout';
  static const String addToCart = 'Add to Cart';
  static const String removeFromCart = 'Remove from Cart';
  static const String subtotal = 'Subtotal';
  static const String total = 'Total';
  static const String placeOrder = 'Place Order';
  static const String payNow = 'Pay Now';
  static const String promoCode = 'Promo Code';
  static const String applyPromo = 'Apply';
  
  // Order Status
  static const String orderPlaced = 'Order Placed';
  static const String orderConfirmed = 'Order Confirmed';
  static const String preparing = 'Preparing';
  static const String ready = 'Ready for Pickup';
  static const String pickedUp = 'Picked Up';
  static const String onTheWay = 'On the Way';
  static const String delivered = 'Delivered';
  static const String cancelled = 'Cancelled';
  
  // Errors
  static const String networkError = 'Network error. Please check your connection.';
  static const String serverError = 'Server error. Please try again later.';
  static const String unknownError = 'An unknown error occurred.';
  static const String validationError = 'Please check your input.';
}

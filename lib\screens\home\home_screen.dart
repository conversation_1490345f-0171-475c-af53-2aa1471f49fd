import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:carousel_slider/carousel_slider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/restaurant_provider.dart';
import '../../utils/constants.dart';
import '../../widgets/restaurant_card.dart';
import '../../widgets/category_chip.dart';
import '../../widgets/promotional_banner.dart';
import '../location/location_selection_screen.dart';
import '../search/search_screen.dart';
import '../cart/cart_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final List<String> _categories = [
    'All',
    'Pizza',
    'Burger',
    'Indian',
    'Chinese',
    'Italian',
    'Mexican',
    'Thai',
    'Fast Food',
    'Desserts',
  ];

  String _selectedCategory = 'All';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<RestaurantProvider>(context, listen: false).loadRestaurants();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: () async {
            await Provider.of<RestaurantProvider>(context, listen: false).loadRestaurants();
          },
          child: CustomScrollView(
            slivers: [
              // App Bar
              SliverAppBar(
                floating: true,
                backgroundColor: Colors.white,
                elevation: 0,
                title: _buildHeader(),
                automaticallyImplyLeading: false,
                expandedHeight: 120,
                flexibleSpace: FlexibleSpaceBar(
                  background: Container(
                    color: Colors.white,
                    padding: const EdgeInsets.only(
                      left: AppDimensions.paddingM,
                      right: AppDimensions.paddingM,
                      bottom: AppDimensions.paddingS,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        _buildSearchBar(),
                      ],
                    ),
                  ),
                ),
              ),

              // Promotional Banners
              SliverToBoxAdapter(
                child: _buildPromotionalBanners(),
              ),

              // Categories
              SliverToBoxAdapter(
                child: _buildCategories(),
              ),

              // Featured Restaurants Section
              SliverToBoxAdapter(
                child: _buildSectionHeader('Featured Restaurants'),
              ),

              // Featured Restaurants
              Consumer<RestaurantProvider>(
                builder: (context, restaurantProvider, child) {
                  if (restaurantProvider.isLoading) {
                    return const SliverToBoxAdapter(
                      child: Center(
                        child: Padding(
                          padding: EdgeInsets.all(AppDimensions.paddingL),
                          child: CircularProgressIndicator(),
                        ),
                      ),
                    );
                  }

                  final featuredRestaurants = restaurantProvider.featuredRestaurants;

                  return SliverToBoxAdapter(
                    child: SizedBox(
                      height: 280,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
                        itemCount: featuredRestaurants.length,
                        itemBuilder: (context, index) {
                          return Padding(
                            padding: const EdgeInsets.only(right: AppDimensions.marginM),
                            child: SizedBox(
                              width: 250,
                              child: RestaurantCard(
                                restaurant: featuredRestaurants[index],
                                isHorizontal: true,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  );
                },
              ),

              // All Restaurants Section
              SliverToBoxAdapter(
                child: _buildSectionHeader('All Restaurants'),
              ),

              // All Restaurants
              Consumer<RestaurantProvider>(
                builder: (context, restaurantProvider, child) {
                  if (restaurantProvider.isLoading) {
                    return const SliverToBoxAdapter(
                      child: SizedBox.shrink(),
                    );
                  }

                  final restaurants = _selectedCategory == 'All'
                      ? restaurantProvider.restaurants
                      : restaurantProvider.getRestaurantsByCategory(_selectedCategory);

                  return SliverPadding(
                    padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
                    sliver: SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          return Padding(
                            padding: const EdgeInsets.only(bottom: AppDimensions.marginM),
                            child: RestaurantCard(
                              restaurant: restaurants[index],
                              isHorizontal: false,
                            ),
                          );
                        },
                        childCount: restaurants.length,
                      ),
                    ),
                  );
                },
              ),

              // Bottom padding
              const SliverToBoxAdapter(
                child: SizedBox(height: 100),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const LocationSelectionScreen(),
                    ),
                  );
                },
                child: Row(
                  children: [
                    const Icon(
                      Icons.location_on,
                      color: AppColors.primary,
                      size: 20,
                    ),
                    const SizedBox(width: AppDimensions.marginS),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'Deliver to',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                          Text(
                            authProvider.user?.addresses.isNotEmpty == true
                                ? authProvider.user!.addresses.first.label
                                : 'Select Location',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    const Icon(
                      Icons.keyboard_arrow_down,
                      color: AppColors.textSecondary,
                    ),
                  ],
                ),
              ),
            ),
            IconButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const CartScreen(),
                  ),
                );
              },
              icon: const Icon(
                Icons.shopping_cart_outlined,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildSearchBar() {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const SearchScreen(),
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingM,
          vertical: AppDimensions.paddingS,
        ),
        decoration: BoxDecoration(
          color: AppColors.background,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(color: AppColors.textHint.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            const Icon(
              Icons.search,
              color: AppColors.textSecondary,
            ),
            const SizedBox(width: AppDimensions.marginS),
            Text(
              'Search restaurants, dishes...',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPromotionalBanners() {
    final banners = [
      PromotionalBanner(
        title: '50% OFF',
        subtitle: 'On your first order',
        imageUrl: 'https://via.placeholder.com/400x200/FF6B6B/FFFFFF?text=50%25+OFF',
        backgroundColor: AppColors.primary,
      ),
      PromotionalBanner(
        title: 'Free Delivery',
        subtitle: 'On orders above \$25',
        imageUrl: 'https://via.placeholder.com/400x200/4ECDC4/FFFFFF?text=Free+Delivery',
        backgroundColor: AppColors.secondary,
      ),
      PromotionalBanner(
        title: 'New Restaurant',
        subtitle: 'Try our latest partner',
        imageUrl: 'https://via.placeholder.com/400x200/45B7D1/FFFFFF?text=New+Restaurant',
        backgroundColor: AppColors.info,
      ),
    ];

    return Container(
      margin: const EdgeInsets.symmetric(vertical: AppDimensions.marginM),
      child: CarouselSlider(
        options: CarouselOptions(
          height: 160,
          autoPlay: true,
          autoPlayInterval: const Duration(seconds: 5),
          enlargeCenterPage: true,
          viewportFraction: 0.9,
        ),
        items: banners.map((banner) => banner).toList(),
      ),
    );
  }

  Widget _buildCategories() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: AppDimensions.marginM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
            child: Text(
              'Categories',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: AppDimensions.marginM),
          SizedBox(
            height: 40,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: AppDimensions.paddingM),
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                return Padding(
                  padding: const EdgeInsets.only(right: AppDimensions.marginS),
                  child: CategoryChip(
                    label: category,
                    isSelected: _selectedCategory == category,
                    onTap: () {
                      setState(() {
                        _selectedCategory = category;
                      });
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          TextButton(
            onPressed: () {
              // Navigate to see all
            },
            child: Text(
              'See All',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

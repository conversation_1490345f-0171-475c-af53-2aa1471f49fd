import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/cart.dart';
import '../models/restaurant.dart';
import '../utils/constants.dart';

class CartProvider with ChangeNotifier {
  Cart? _cart;
  bool _isLoading = false;

  Cart? get cart => _cart;
  bool get isLoading => _isLoading;
  bool get hasItems => _cart != null && _cart!.isNotEmpty;
  int get itemCount => _cart?.totalItems ?? 0;
  double get total => _cart?.total ?? 0.0;
  double get subtotal => _cart?.subtotal ?? 0.0;
  double get deliveryFee => _cart?.deliveryFee ?? 0.0;
  double get tax => _cart?.tax ?? 0.0;
  List<CartItem> get items => _cart?.items ?? [];

  CartProvider() {
    _loadCartFromStorage();
  }

  Future<void> _loadCartFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartData = prefs.getString(AppConstants.cartDataKey);

      if (cartData != null) {
        _cart = Cart.fromJson(_parseJson(cartData));
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading cart from storage: $e');
    }
  }

  Map<String, dynamic> _parseJson(String jsonString) {
    // In a real app, you would use dart:convert
    // For now, returning a mock cart data structure
    return {
      'restaurant_id': '1',
      'restaurant_name': 'Mock Restaurant',
      'items': [],
      'delivery_fee': AppConstants.defaultDeliveryFee,
      'service_fee': AppConstants.defaultServiceFee,
      'tax': 0.0,
      'promo_code': null,
      'discount': 0.0,
    };
  }

  Future<void> _saveCartToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      if (_cart != null) {
        await prefs.setString(
            AppConstants.cartDataKey, _cartToJsonString(_cart!));
      } else {
        await prefs.remove(AppConstants.cartDataKey);
      }
    } catch (e) {
      debugPrint('Error saving cart to storage: $e');
    }
  }

  String _cartToJsonString(Cart cart) {
    // In a real app, you would use dart:convert
    // For now, returning a mock JSON string
    return '{"restaurant_id":"${cart.restaurantId}","restaurant_name":"${cart.restaurantName}","items":[]}';
  }

  Future<bool> addItem(
      MenuItem menuItem,
      int quantity,
      List<SelectedCustomization> customizations,
      String? specialInstructions) async {
    _setLoading(true);

    try {
      // Create cart item
      final cartItem = CartItem(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        menuItemId: menuItem.id,
        restaurantId:
            'restaurant_id', // This should come from the restaurant context
        restaurantName:
            'Restaurant Name', // This should come from the restaurant context
        name: menuItem.name,
        description: menuItem.description,
        basePrice: menuItem.price,
        imageUrl: menuItem.imageUrl,
        quantity: quantity,
        selectedCustomizations: customizations,
        specialInstructions: specialInstructions,
      );

      // If cart is empty or from different restaurant, create new cart
      if (_cart == null || _cart!.isEmpty) {
        _cart = Cart(
          restaurantId:
              'restaurant_id', // This should come from the restaurant context
          restaurantName:
              'Restaurant Name', // This should come from the restaurant context
          items: [cartItem],
          deliveryFee: AppConstants.defaultDeliveryFee,
          serviceFee: AppConstants.defaultServiceFee,
          tax: 0.0,
        );
      } else {
        // Check if item is from same restaurant
        // In a real app, you would get restaurant ID from context
        _cart = _cart!.addItem(cartItem);
      }

      // Calculate tax
      _cart =
          _cart!.copyWith(tax: _cart!.subtotal * AppConstants.defaultTaxRate);

      await _saveCartToStorage();
      _setLoading(false);
      return true;
    } catch (e) {
      _setLoading(false);
      debugPrint('Add item error: $e');
      return false;
    }
  }

  Future<void> removeItem(String cartItemId) async {
    if (_cart == null) return;

    _setLoading(true);

    try {
      _cart = _cart!.removeItem(cartItemId);

      // If cart is empty, clear it
      if (_cart!.isEmpty) {
        _cart = null;
      } else {
        // Recalculate tax
        _cart =
            _cart!.copyWith(tax: _cart!.subtotal * AppConstants.defaultTaxRate);
      }

      await _saveCartToStorage();
      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      debugPrint('Remove item error: $e');
    }
  }

  Future<void> updateItemQuantity(String cartItemId, int quantity) async {
    if (_cart == null) return;

    _setLoading(true);

    try {
      _cart = _cart!.updateItemQuantity(cartItemId, quantity);

      // If cart is empty after update, clear it
      if (_cart!.isEmpty) {
        _cart = null;
      } else {
        // Recalculate tax
        _cart =
            _cart!.copyWith(tax: _cart!.subtotal * AppConstants.defaultTaxRate);
      }

      await _saveCartToStorage();
      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      debugPrint('Update item quantity error: $e');
    }
  }

  Future<void> clearCart() async {
    _setLoading(true);

    try {
      _cart = null;
      await _saveCartToStorage();
      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      debugPrint('Clear cart error: $e');
    }
  }

  Future<bool> applyPromoCode(String promoCode) async {
    if (_cart == null) return false;

    _setLoading(true);

    try {
      // Simulate API call to validate promo code
      await Future.delayed(const Duration(seconds: 1));

      // Mock promo code validation
      double discount = 0.0;
      if (promoCode.toLowerCase() == 'save10') {
        discount = _cart!.subtotal * 0.1; // 10% discount
      } else if (promoCode.toLowerCase() == 'free5') {
        discount = 5.0; // $5 off
      } else {
        _setLoading(false);
        return false; // Invalid promo code
      }

      _cart = _cart!.applyPromoCode(promoCode, discount);
      await _saveCartToStorage();
      _setLoading(false);
      return true;
    } catch (e) {
      _setLoading(false);
      debugPrint('Apply promo code error: $e');
      return false;
    }
  }

  Future<void> removePromoCode() async {
    if (_cart == null) return;

    _setLoading(true);

    try {
      _cart = _cart!.removePromoCode();
      await _saveCartToStorage();
      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      debugPrint('Remove promo code error: $e');
    }
  }

  // Check if cart is from different restaurant
  bool isFromDifferentRestaurant(String restaurantId) {
    if (_cart == null || _cart!.isEmpty) return false;
    return _cart!.restaurantId != restaurantId;
  }

  // Get cart item by menu item ID and customizations
  CartItem? getCartItem(
      String menuItemId, List<SelectedCustomization> customizations) {
    if (_cart == null) return null;

    try {
      return _cart!.items.firstWhere(
        (item) =>
            item.menuItemId == menuItemId &&
            _customizationsMatch(item.selectedCustomizations, customizations),
      );
    } catch (e) {
      return null;
    }
  }

  bool _customizationsMatch(List<SelectedCustomization> customizations1,
      List<SelectedCustomization> customizations2) {
    if (customizations1.length != customizations2.length) return false;

    for (var customization1 in customizations1) {
      final match = customizations2.any((customization2) =>
          customization1.customizationId == customization2.customizationId &&
          customization1.selectedOptions.length ==
              customization2.selectedOptions.length &&
          customization1.selectedOptions.every((option1) => customization2
              .selectedOptions
              .any((option2) => option1.id == option2.id)));
      if (!match) return false;
    }

    return true;
  }

  // Update delivery fee
  void updateDeliveryFee(double deliveryFee) {
    if (_cart != null) {
      _cart = _cart!.copyWith(deliveryFee: deliveryFee);
      _saveCartToStorage();
      notifyListeners();
    }
  }

  // Update service fee
  void updateServiceFee(double serviceFee) {
    if (_cart != null) {
      _cart = _cart!.copyWith(serviceFee: serviceFee);
      _saveCartToStorage();
      notifyListeners();
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Get item quantity in cart
  int getItemQuantity(
      String menuItemId, List<SelectedCustomization> customizations) {
    final cartItem = getCartItem(menuItemId, customizations);
    return cartItem?.quantity ?? 0;
  }

  // Check if minimum order value is met
  bool isMinimumOrderMet(double minimumOrderValue) {
    if (_cart == null) return false;
    return _cart!.subtotal >= minimumOrderValue;
  }

  // Get remaining amount to meet minimum order
  double getRemainingForMinimumOrder(double minimumOrderValue) {
    if (_cart == null) return minimumOrderValue;
    final remaining = minimumOrderValue - _cart!.subtotal;
    return remaining > 0 ? remaining : 0;
  }

  // Add item with CartItem object
  Future<void> addCartItem(CartItem cartItem) async {
    _setLoading(true);

    try {
      // If cart is empty or from different restaurant, create new cart
      if (_cart == null || _cart!.isEmpty) {
        _cart = Cart(
          restaurantId: cartItem.restaurantId,
          restaurantName: cartItem.restaurantName,
          items: [cartItem],
          deliveryFee: 2.99, // Default delivery fee
          serviceFee: 0.99, // Default service fee
          tax: 0.0,
        );
      } else {
        // Check if item already exists
        final existingItemIndex = _cart!.items
            .indexWhere((item) => item.menuItemId == cartItem.menuItemId);

        if (existingItemIndex != -1) {
          // Update quantity of existing item
          final existingItem = _cart!.items[existingItemIndex];
          final updatedItem = existingItem.copyWith(
            quantity: existingItem.quantity + cartItem.quantity,
          );
          final updatedItems = List<CartItem>.from(_cart!.items);
          updatedItems[existingItemIndex] = updatedItem;
          _cart = _cart!.copyWith(items: updatedItems);
        } else {
          // Add new item
          _cart = _cart!.copyWith(items: [..._cart!.items, cartItem]);
        }
      }

      // Calculate tax
      _cart = _cart!.copyWith(tax: _cart!.subtotal * 0.08); // 8% tax

      await _saveCartToStorage();
      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      debugPrint('Add item error: $e');
    }
  }

  // Update quantity
  Future<void> updateQuantity(String cartItemId, int quantity) async {
    if (_cart == null) return;

    _setLoading(true);

    try {
      if (quantity <= 0) {
        await removeItem(cartItemId);
        return;
      }

      final updatedItems = _cart!.items.map((item) {
        if (item.id == cartItemId) {
          return item.copyWith(quantity: quantity);
        }
        return item;
      }).toList();

      _cart = _cart!.copyWith(items: updatedItems);

      // Recalculate tax
      _cart = _cart!.copyWith(tax: _cart!.subtotal * 0.08);

      await _saveCartToStorage();
      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      debugPrint('Update quantity error: $e');
    }
  }

  // Get cart item by menu item ID
  CartItem? getItemByMenuItemId(String menuItemId) {
    if (_cart == null) return null;

    try {
      return _cart!.items.firstWhere((item) => item.menuItemId == menuItemId);
    } catch (e) {
      return null;
    }
  }
}

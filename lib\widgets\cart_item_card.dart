import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import '../models/cart.dart';
import '../providers/cart_provider.dart';
import '../utils/constants.dart';

class CartItemCard extends StatelessWidget {
  final CartItem cartItem;

  const CartItemCard({
    super.key,
    required this.cartItem,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Row(
          children: [
            // Item Image
            ClipRRect(
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
              child: CachedNetworkImage(
                imageUrl: cartItem.imageUrl ?? '',
                width: 80,
                height: 80,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  width: 80,
                  height: 80,
                  color: AppColors.background,
                  child: const Icon(
                    Icons.fastfood,
                    color: AppColors.textSecondary,
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  width: 80,
                  height: 80,
                  color: AppColors.background,
                  child: const Icon(
                    Icons.fastfood,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ),

            const SizedBox(width: AppDimensions.marginM),

            // Item Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Item Name
                  Text(
                    cartItem.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 4),

                  // Restaurant Name
                  Text(
                    cartItem.restaurantName,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                  ),

                  const SizedBox(height: AppDimensions.marginS),

                  // Customizations
                  if (cartItem.selectedCustomizations.isNotEmpty)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ...cartItem.selectedCustomizations.map((customization) {
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 2),
                            child: Text(
                              '• ${customization.customizationName}: ${customization.selectedOptions.map((opt) => opt.name).join(", ")}',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: AppColors.textSecondary,
                                    fontSize: 11,
                                  ),
                            ),
                          );
                        }).toList(),
                        const SizedBox(height: AppDimensions.marginS),
                      ],
                    ),

                  // Price and Quantity Controls
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Price
                      Text(
                        '\$${(cartItem.price * cartItem.quantity).toStringAsFixed(2)}',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primary,
                                ),
                      ),

                      // Quantity Controls
                      Row(
                        children: [
                          // Decrease Button
                          InkWell(
                            onTap: () {
                              if (cartItem.quantity > 1) {
                                Provider.of<CartProvider>(context,
                                        listen: false)
                                    .updateQuantity(
                                        cartItem.id, cartItem.quantity - 1);
                              } else {
                                _showRemoveDialog(context);
                              }
                            },
                            borderRadius:
                                BorderRadius.circular(AppDimensions.radiusS),
                            child: Container(
                              width: 32,
                              height: 32,
                              decoration: BoxDecoration(
                                border: Border.all(color: AppColors.primary),
                                borderRadius: BorderRadius.circular(
                                    AppDimensions.radiusS),
                              ),
                              child: Icon(
                                cartItem.quantity > 1
                                    ? Icons.remove
                                    : Icons.delete_outline,
                                size: 18,
                                color: cartItem.quantity > 1
                                    ? AppColors.primary
                                    : AppColors.error,
                              ),
                            ),
                          ),

                          // Quantity
                          Container(
                            width: 50,
                            alignment: Alignment.center,
                            child: Text(
                              '${cartItem.quantity}',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ),

                          // Increase Button
                          InkWell(
                            onTap: () {
                              Provider.of<CartProvider>(context, listen: false)
                                  .updateQuantity(
                                      cartItem.id, cartItem.quantity + 1);
                            },
                            borderRadius:
                                BorderRadius.circular(AppDimensions.radiusS),
                            child: Container(
                              width: 32,
                              height: 32,
                              decoration: BoxDecoration(
                                color: AppColors.primary,
                                borderRadius: BorderRadius.circular(
                                    AppDimensions.radiusS),
                              ),
                              child: const Icon(
                                Icons.add,
                                size: 18,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showRemoveDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Remove Item'),
          content: Text('Remove ${cartItem.name} from cart?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                Provider.of<CartProvider>(context, listen: false)
                    .removeItem(cartItem.id);
              },
              child: Text(
                'Remove',
                style: TextStyle(color: AppColors.error),
              ),
            ),
          ],
        );
      },
    );
  }
}

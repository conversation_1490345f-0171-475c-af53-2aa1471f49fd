import 'package:equatable/equatable.dart';
import '../../models/cart.dart';

abstract class CartEvent extends Equatable {
  const CartEvent();

  @override
  List<Object?> get props => [];
}

class CartStarted extends CartEvent {}

class CartItemAdded extends CartEvent {
  final CartItem item;

  const CartItemAdded({required this.item});

  @override
  List<Object> get props => [item];
}

class CartItemRemoved extends CartEvent {
  final String itemId;

  const CartItemRemoved({required this.itemId});

  @override
  List<Object> get props => [itemId];
}

class CartItemQuantityUpdated extends CartEvent {
  final String itemId;
  final int quantity;

  const CartItemQuantityUpdated({
    required this.itemId,
    required this.quantity,
  });

  @override
  List<Object> get props => [itemId, quantity];
}

class CartCleared extends CartEvent {}

class CartPromoCodeApplied extends CartEvent {
  final String promoCode;

  const CartPromoCodeApplied({required this.promoCode});

  @override
  List<Object> get props => [promoCode];
}

class CartPromoCodeRemoved extends CartEvent {}

class CartDeliveryAddressUpdated extends CartEvent {
  final String address;

  const CartDeliveryAddressUpdated({required this.address});

  @override
  List<Object> get props => [address];
}

class CartSpecialInstructionsUpdated extends CartEvent {
  final String instructions;

  const CartSpecialInstructionsUpdated({required this.instructions});

  @override
  List<Object> get props => [instructions];
}

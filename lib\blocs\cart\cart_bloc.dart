import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import '../../models/cart.dart';
import '../../utils/constants.dart';
import 'cart_event.dart';
import 'cart_state.dart';

class CartBloc extends Bloc<CartEvent, CartState> {
  CartBloc() : super(CartInitial()) {
    on<CartStarted>(_onCartStarted);
    on<CartItemAdded>(_onCartItemAdded);
    on<CartItemRemoved>(_onCartItemRemoved);
    on<CartItemQuantityUpdated>(_onCartItemQuantityUpdated);
    on<CartCleared>(_onCartCleared);
    on<CartPromoCodeApplied>(_onCartPromoCodeApplied);
    on<CartPromoCodeRemoved>(_onCartPromoCodeRemoved);
    on<CartDeliveryAddressUpdated>(_onCartDeliveryAddressUpdated);
    on<CartSpecialInstructionsUpdated>(_onCartSpecialInstructionsUpdated);
  }

  Future<void> _onCartStarted(CartStarted event, Emitter<CartState> emit) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartData = prefs.getString(AppConstants.cartDataKey);
      
      if (cartData != null) {
        // Load cart from storage
        // For now, emit empty cart
        emit(const CartLoaded(
          items: [],
          subtotal: 0.0,
          deliveryFee: 2.99,
          tax: 0.0,
          discount: 0.0,
          total: 2.99,
          itemCount: 0,
        ));
      } else {
        emit(const CartLoaded(
          items: [],
          subtotal: 0.0,
          deliveryFee: 2.99,
          tax: 0.0,
          discount: 0.0,
          total: 2.99,
          itemCount: 0,
        ));
      }
    } catch (e) {
      debugPrint('Error loading cart: $e');
      emit(const CartError(message: 'Failed to load cart'));
    }
  }

  Future<void> _onCartItemAdded(
      CartItemAdded event, Emitter<CartState> emit) async {
    final currentState = state;
    if (currentState is CartLoaded) {
      final items = List<CartItem>.from(currentState.items);
      
      // Check if item already exists
      final existingIndex = items.indexWhere((item) => item.id == event.item.id);
      
      if (existingIndex != -1) {
        // Update quantity
        items[existingIndex] = items[existingIndex].copyWith(
          quantity: items[existingIndex].quantity + event.item.quantity,
        );
      } else {
        // Add new item
        items.add(event.item);
      }

      final calculations = _calculateTotals(items, currentState.deliveryFee, currentState.discount);
      
      final newState = currentState.copyWith(
        items: items,
        subtotal: calculations['subtotal']!,
        tax: calculations['tax']!,
        total: calculations['total']!,
        itemCount: calculations['itemCount']!.toInt(),
      );

      emit(newState);
      await _saveCartToStorage(newState);
    }
  }

  Future<void> _onCartItemRemoved(
      CartItemRemoved event, Emitter<CartState> emit) async {
    final currentState = state;
    if (currentState is CartLoaded) {
      final items = List<CartItem>.from(currentState.items);
      items.removeWhere((item) => item.id == event.itemId);

      final calculations = _calculateTotals(items, currentState.deliveryFee, currentState.discount);
      
      final newState = currentState.copyWith(
        items: items,
        subtotal: calculations['subtotal']!,
        tax: calculations['tax']!,
        total: calculations['total']!,
        itemCount: calculations['itemCount']!.toInt(),
      );

      emit(newState);
      await _saveCartToStorage(newState);
    }
  }

  Future<void> _onCartItemQuantityUpdated(
      CartItemQuantityUpdated event, Emitter<CartState> emit) async {
    final currentState = state;
    if (currentState is CartLoaded) {
      final items = List<CartItem>.from(currentState.items);
      final index = items.indexWhere((item) => item.id == event.itemId);
      
      if (index != -1) {
        if (event.quantity <= 0) {
          items.removeAt(index);
        } else {
          items[index] = items[index].copyWith(quantity: event.quantity);
        }

        final calculations = _calculateTotals(items, currentState.deliveryFee, currentState.discount);
        
        final newState = currentState.copyWith(
          items: items,
          subtotal: calculations['subtotal']!,
          tax: calculations['tax']!,
          total: calculations['total']!,
          itemCount: calculations['itemCount']!.toInt(),
        );

        emit(newState);
        await _saveCartToStorage(newState);
      }
    }
  }

  Future<void> _onCartCleared(CartCleared event, Emitter<CartState> emit) async {
    const newState = CartLoaded(
      items: [],
      subtotal: 0.0,
      deliveryFee: 2.99,
      tax: 0.0,
      discount: 0.0,
      total: 2.99,
      itemCount: 0,
    );

    emit(newState);
    await _saveCartToStorage(newState);
  }

  Future<void> _onCartPromoCodeApplied(
      CartPromoCodeApplied event, Emitter<CartState> emit) async {
    final currentState = state;
    if (currentState is CartLoaded) {
      // Mock promo code validation
      double discount = 0.0;
      
      switch (event.promoCode.toUpperCase()) {
        case 'SAVE10':
          discount = currentState.subtotal * 0.1;
          break;
        case 'SAVE20':
          discount = currentState.subtotal * 0.2;
          break;
        case 'FREEDEL':
          discount = currentState.deliveryFee;
          break;
        default:
          emit(const CartPromoCodeError(message: 'Invalid promo code'));
          return;
      }

      final calculations = _calculateTotals(currentState.items, currentState.deliveryFee, discount);
      
      final newState = currentState.copyWith(
        discount: discount,
        total: calculations['total']!,
        promoCode: event.promoCode,
      );

      emit(newState);
      await _saveCartToStorage(newState);
    }
  }

  Future<void> _onCartPromoCodeRemoved(
      CartPromoCodeRemoved event, Emitter<CartState> emit) async {
    final currentState = state;
    if (currentState is CartLoaded) {
      final calculations = _calculateTotals(currentState.items, currentState.deliveryFee, 0.0);
      
      final newState = currentState.copyWith(
        discount: 0.0,
        total: calculations['total']!,
        promoCode: null,
      );

      emit(newState);
      await _saveCartToStorage(newState);
    }
  }

  Future<void> _onCartDeliveryAddressUpdated(
      CartDeliveryAddressUpdated event, Emitter<CartState> emit) async {
    final currentState = state;
    if (currentState is CartLoaded) {
      final newState = currentState.copyWith(deliveryAddress: event.address);
      emit(newState);
      await _saveCartToStorage(newState);
    }
  }

  Future<void> _onCartSpecialInstructionsUpdated(
      CartSpecialInstructionsUpdated event, Emitter<CartState> emit) async {
    final currentState = state;
    if (currentState is CartLoaded) {
      final newState = currentState.copyWith(specialInstructions: event.instructions);
      emit(newState);
      await _saveCartToStorage(newState);
    }
  }

  Map<String, double> _calculateTotals(List<CartItem> items, double deliveryFee, double discount) {
    final subtotal = items.fold<double>(0.0, (sum, item) => sum + (item.price * item.quantity));
    final tax = subtotal * 0.08; // 8% tax
    final itemCount = items.fold<double>(0.0, (sum, item) => sum + item.quantity);
    final total = subtotal + deliveryFee + tax - discount;

    return {
      'subtotal': subtotal,
      'tax': tax,
      'total': total,
      'itemCount': itemCount,
    };
  }

  Future<void> _saveCartToStorage(CartLoaded cartState) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      // In a real app, you'd serialize the cart data properly
      await prefs.setString(AppConstants.cartDataKey, 'cart_data');
    } catch (e) {
      debugPrint('Error saving cart: $e');
    }
  }
}

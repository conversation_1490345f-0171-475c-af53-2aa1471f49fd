import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import '../../models/order.dart';
import '../../models/cart.dart';
import 'order_event.dart';
import 'order_state.dart';

class OrderBloc extends Bloc<OrderEvent, OrderState> {
  OrderBloc() : super(OrderInitial()) {
    on<OrderStarted>(_onOrderStarted);
    on<OrderPlaceRequested>(_onOrderPlaceRequested);
    on<OrderHistoryRequested>(_onOrderHistoryRequested);
    on<OrderDetailsRequested>(_onOrderDetailsRequested);
    on<OrderTrackingRequested>(_onOrderTrackingRequested);
    on<OrderCancelRequested>(_onOrderCancelRequested);
    on<OrderRateRequested>(_onOrderRateRequested);
    on<OrderReorderRequested>(_onOrderReorderRequested);
    on<OrderStatusUpdated>(_onOrderStatusUpdated);
  }

  Future<void> _onOrderStarted(OrderStarted event, Emitter<OrderState> emit) async {
    // Initialize order state if needed
    emit(OrderInitial());
  }

  Future<void> _onOrderPlaceRequested(
      OrderPlaceRequested event, Emitter<OrderState> emit) async {
    emit(OrderLoading());

    try {
      // Simulate payment processing
      emit(OrderPaymentProcessing(
        orderId: 'temp_${DateTime.now().millisecondsSinceEpoch}',
        paymentMethod: event.paymentMethod,
      ));

      await Future.delayed(const Duration(seconds: 2));

      // Create order
      final order = Order(
        id: 'ORD${DateTime.now().millisecondsSinceEpoch}',
        userId: 'user_1',
        restaurantId: 'restaurant_1',
        restaurantName: 'Pizza Palace',
        items: event.items.map((cartItem) => OrderItem(
          id: cartItem.id,
          name: cartItem.name,
          price: cartItem.price,
          quantity: cartItem.quantity,
          imageUrl: cartItem.imageUrl,
          customizations: cartItem.customizations,
        )).toList(),
        status: OrderStatus.placed,
        totalAmount: event.total,
        deliveryAddress: event.deliveryAddress,
        paymentMethod: event.paymentMethod,
        specialInstructions: event.specialInstructions,
        promoCode: event.promoCode,
        orderDate: DateTime.now(),
        estimatedDeliveryTime: DateTime.now().add(const Duration(minutes: 30)),
      );

      // Simulate successful payment
      emit(OrderPaymentSuccess(
        order: order,
        transactionId: 'TXN${DateTime.now().millisecondsSinceEpoch}',
      ));

      await Future.delayed(const Duration(seconds: 1));

      emit(OrderPlaced(order: order));
    } catch (e) {
      debugPrint('Order placement error: $e');
      emit(const OrderPaymentFailed(message: 'Payment failed. Please try again.'));
    }
  }

  Future<void> _onOrderHistoryRequested(
      OrderHistoryRequested event, Emitter<OrderState> emit) async {
    emit(OrderLoading());

    try {
      await Future.delayed(const Duration(seconds: 1));

      final orders = _getMockOrders();
      emit(OrderHistoryLoaded(orders: orders));
    } catch (e) {
      debugPrint('Error loading order history: $e');
      emit(const OrderError(message: 'Failed to load order history'));
    }
  }

  Future<void> _onOrderDetailsRequested(
      OrderDetailsRequested event, Emitter<OrderState> emit) async {
    emit(OrderLoading());

    try {
      await Future.delayed(const Duration(milliseconds: 500));

      final order = _getMockOrders()
          .firstWhere((order) => order.id == event.orderId);

      emit(OrderDetailsLoaded(order: order));
    } catch (e) {
      debugPrint('Error loading order details: $e');
      emit(const OrderError(message: 'Failed to load order details'));
    }
  }

  Future<void> _onOrderTrackingRequested(
      OrderTrackingRequested event, Emitter<OrderState> emit) async {
    emit(OrderLoading());

    try {
      await Future.delayed(const Duration(milliseconds: 500));

      final order = _getMockOrders()
          .firstWhere((order) => order.id == event.orderId);

      final statusUpdates = _getMockStatusUpdates();
      final deliveryInfo = _getMockDeliveryInfo();

      emit(OrderTrackingLoaded(
        order: order,
        statusUpdates: statusUpdates,
        deliveryInfo: deliveryInfo,
      ));
    } catch (e) {
      debugPrint('Error loading order tracking: $e');
      emit(const OrderError(message: 'Failed to load order tracking'));
    }
  }

  Future<void> _onOrderCancelRequested(
      OrderCancelRequested event, Emitter<OrderState> emit) async {
    emit(OrderLoading());

    try {
      await Future.delayed(const Duration(seconds: 1));

      emit(OrderCancelled(orderId: event.orderId, reason: event.reason));
    } catch (e) {
      debugPrint('Error cancelling order: $e');
      emit(const OrderError(message: 'Failed to cancel order'));
    }
  }

  Future<void> _onOrderRateRequested(
      OrderRateRequested event, Emitter<OrderState> emit) async {
    emit(OrderLoading());

    try {
      await Future.delayed(const Duration(seconds: 1));

      emit(OrderRated(
        orderId: event.orderId,
        rating: event.rating,
        review: event.review,
      ));
    } catch (e) {
      debugPrint('Error rating order: $e');
      emit(const OrderError(message: 'Failed to submit rating'));
    }
  }

  Future<void> _onOrderReorderRequested(
      OrderReorderRequested event, Emitter<OrderState> emit) async {
    emit(OrderLoading());

    try {
      await Future.delayed(const Duration(milliseconds: 500));

      final order = _getMockOrders()
          .firstWhere((order) => order.id == event.orderId);

      // Convert order items to cart items
      final cartItems = order.items.map((orderItem) => CartItem(
        id: orderItem.id,
        name: orderItem.name,
        price: orderItem.price,
        quantity: orderItem.quantity,
        imageUrl: orderItem.imageUrl,
        customizations: orderItem.customizations,
      )).toList();

      emit(OrderReordered(originalOrder: order, cartItems: cartItems));
    } catch (e) {
      debugPrint('Error reordering: $e');
      emit(const OrderError(message: 'Failed to reorder'));
    }
  }

  Future<void> _onOrderStatusUpdated(
      OrderStatusUpdated event, Emitter<OrderState> emit) async {
    // Handle real-time order status updates
    // This would typically be called from a WebSocket or push notification
    final currentState = state;
    if (currentState is OrderTrackingLoaded) {
      // Update the order status and emit new state
      final updatedOrder = Order(
        id: currentState.order.id,
        userId: currentState.order.userId,
        restaurantId: currentState.order.restaurantId,
        restaurantName: currentState.order.restaurantName,
        items: currentState.order.items,
        status: event.status,
        totalAmount: currentState.order.totalAmount,
        deliveryAddress: currentState.order.deliveryAddress,
        paymentMethod: currentState.order.paymentMethod,
        specialInstructions: currentState.order.specialInstructions,
        promoCode: currentState.order.promoCode,
        orderDate: currentState.order.orderDate,
        estimatedDeliveryTime: currentState.order.estimatedDeliveryTime,
        actualDeliveryTime: event.status == OrderStatus.delivered 
            ? DateTime.now() 
            : currentState.order.actualDeliveryTime,
      );

      emit(OrderTrackingLoaded(
        order: updatedOrder,
        statusUpdates: currentState.statusUpdates,
        deliveryInfo: currentState.deliveryInfo,
      ));
    }
  }

  List<Order> _getMockOrders() {
    return [
      Order(
        id: 'ORD001',
        userId: 'user_1',
        restaurantId: 'restaurant_1',
        restaurantName: 'Pizza Palace',
        items: [
          OrderItem(
            id: '1',
            name: 'Margherita Pizza',
            price: 12.99,
            quantity: 2,
            imageUrl: 'https://via.placeholder.com/100x100/FF6B6B/FFFFFF?text=Pizza',
          ),
          OrderItem(
            id: '2',
            name: 'Garlic Bread',
            price: 5.99,
            quantity: 1,
            imageUrl: 'https://via.placeholder.com/100x100/4ECDC4/FFFFFF?text=Bread',
          ),
        ],
        status: OrderStatus.delivered,
        totalAmount: 34.97,
        deliveryAddress: '123 Main St, Apt 4B, New York, NY 10001',
        paymentMethod: 'Credit Card',
        orderDate: DateTime.now().subtract(const Duration(days: 2)),
        estimatedDeliveryTime: DateTime.now().subtract(const Duration(days: 2, hours: 23, minutes: 30)),
        actualDeliveryTime: DateTime.now().subtract(const Duration(days: 2, hours: 23, minutes: 25)),
      ),
      Order(
        id: 'ORD002',
        userId: 'user_1',
        restaurantId: 'restaurant_2',
        restaurantName: 'Burger Barn',
        items: [
          OrderItem(
            id: '3',
            name: 'Classic Burger',
            price: 8.99,
            quantity: 1,
            imageUrl: 'https://via.placeholder.com/100x100/45B7D1/FFFFFF?text=Burger',
          ),
        ],
        status: OrderStatus.onTheWay,
        totalAmount: 12.48,
        deliveryAddress: '123 Main St, Apt 4B, New York, NY 10001',
        paymentMethod: 'UPI',
        orderDate: DateTime.now().subtract(const Duration(minutes: 45)),
        estimatedDeliveryTime: DateTime.now().add(const Duration(minutes: 15)),
      ),
    ];
  }

  List<OrderStatusUpdate> _getMockStatusUpdates() {
    return [
      OrderStatusUpdate(
        status: OrderStatus.placed,
        timestamp: DateTime.now().subtract(const Duration(minutes: 45)),
        description: 'Order placed successfully',
      ),
      OrderStatusUpdate(
        status: OrderStatus.confirmed,
        timestamp: DateTime.now().subtract(const Duration(minutes: 40)),
        description: 'Restaurant confirmed your order',
      ),
      OrderStatusUpdate(
        status: OrderStatus.preparing,
        timestamp: DateTime.now().subtract(const Duration(minutes: 35)),
        description: 'Your food is being prepared',
      ),
      OrderStatusUpdate(
        status: OrderStatus.pickedUp,
        timestamp: DateTime.now().subtract(const Duration(minutes: 10)),
        description: 'Order picked up by delivery partner',
      ),
      OrderStatusUpdate(
        status: OrderStatus.onTheWay,
        timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
        description: 'On the way to your location',
      ),
    ];
  }

  DeliveryInfo _getMockDeliveryInfo() {
    return const DeliveryInfo(
      deliveryPersonName: 'John Smith',
      deliveryPersonPhone: '+1234567890',
      deliveryPersonImage: 'https://via.placeholder.com/100x100/95A5A6/FFFFFF?text=JS',
      currentLatitude: 40.7128,
      currentLongitude: -74.0060,
      vehicleNumber: 'ABC-1234',
    );
  }
}

import 'package:equatable/equatable.dart';

abstract class RestaurantEvent extends Equatable {
  const RestaurantEvent();

  @override
  List<Object?> get props => [];
}

class RestaurantLoadRequested extends RestaurantEvent {}

class RestaurantSearchRequested extends RestaurantEvent {
  final String query;

  const RestaurantSearchRequested({required this.query});

  @override
  List<Object> get props => [query];
}

class RestaurantFilterApplied extends RestaurantEvent {
  final String? cuisine;
  final double? minRating;
  final double? maxDeliveryTime;
  final bool? freeDelivery;
  final bool? hasOffers;
  final bool? vegetarianOnly;

  const RestaurantFilterApplied({
    this.cuisine,
    this.minRating,
    this.maxDeliveryTime,
    this.freeDelivery,
    this.hasOffers,
    this.vegetarianOnly,
  });

  @override
  List<Object?> get props => [
        cuisine,
        minRating,
        maxDeliveryTime,
        freeDelivery,
        hasOffers,
        vegetarianOnly,
      ];
}

class RestaurantDetailsRequested extends RestaurantEvent {
  final String restaurantId;

  const RestaurantDetailsRequested({required this.restaurantId});

  @override
  List<Object> get props => [restaurantId];
}

class RestaurantCategorySelected extends RestaurantEvent {
  final String category;

  const RestaurantCategorySelected({required this.category});

  @override
  List<Object> get props => [category];
}

class RestaurantRefreshRequested extends RestaurantEvent {}

class RestaurantLocationUpdated extends RestaurantEvent {
  final double latitude;
  final double longitude;

  const RestaurantLocationUpdated({
    required this.latitude,
    required this.longitude,
  });

  @override
  List<Object> get props => [latitude, longitude];
}

import 'package:equatable/equatable.dart';
import 'location_event.dart';

abstract class LocationState extends Equatable {
  const LocationState();

  @override
  List<Object?> get props => [];
}

class LocationInitial extends LocationState {}

class LocationLoading extends LocationState {}

class LocationPermissionDenied extends LocationState {
  final String message;

  const LocationPermissionDenied({required this.message});

  @override
  List<Object> get props => [message];
}

class LocationPermissionGranted extends LocationState {}

class LocationLoaded extends LocationState {
  final String address;
  final double latitude;
  final double longitude;

  const LocationLoaded({
    required this.address,
    required this.latitude,
    required this.longitude,
  });

  @override
  List<Object> get props => [address, latitude, longitude];
}

class LocationSelected extends LocationState {
  final String address;
  final double latitude;
  final double longitude;

  const LocationSelected({
    required this.address,
    required this.latitude,
    required this.longitude,
  });

  @override
  List<Object> get props => [address, latitude, longitude];
}

class LocationSavedAddressesLoaded extends LocationState {
  final List<SavedAddress> addresses;
  final SavedAddress? currentAddress;

  const LocationSavedAddressesLoaded({
    required this.addresses,
    this.currentAddress,
  });

  @override
  List<Object?> get props => [addresses, currentAddress];
}

class LocationAddressAddedState extends LocationState {
  final SavedAddress address;

  const LocationAddressAddedState({required this.address});

  @override
  List<Object> get props => [address];
}

class LocationAddressUpdatedState extends LocationState {
  final SavedAddress address;

  const LocationAddressUpdatedState({required this.address});

  @override
  List<Object> get props => [address];
}

class LocationAddressDeletedState extends LocationState {
  final String addressId;

  const LocationAddressDeletedState({required this.addressId});

  @override
  List<Object> get props => [addressId];
}

class LocationSearchResults extends LocationState {
  final List<AddressSearchResult> results;
  final String query;

  const LocationSearchResults({
    required this.results,
    required this.query,
  });

  @override
  List<Object> get props => [results, query];
}

class LocationMapPositionUpdated extends LocationState {
  final double latitude;
  final double longitude;
  final String? address;

  const LocationMapPositionUpdated({
    required this.latitude,
    required this.longitude,
    this.address,
  });

  @override
  List<Object?> get props => [latitude, longitude, address];
}

class LocationError extends LocationState {
  final String message;

  const LocationError({required this.message});

  @override
  List<Object> get props => [message];
}

// Model for address search results
class AddressSearchResult {
  final String address;
  final String? description;
  final double latitude;
  final double longitude;

  const AddressSearchResult({
    required this.address,
    this.description,
    required this.latitude,
    required this.longitude,
  });
}

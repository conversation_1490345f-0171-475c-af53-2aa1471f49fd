import 'restaurant.dart';

class Cart {
  final String restaurantId;
  final String restaurantName;
  final List<CartItem> items;
  final double deliveryFee;
  final double serviceFee;
  final double tax;
  final String? promoCode;
  final double discount;

  Cart({
    required this.restaurantId,
    required this.restaurantName,
    this.items = const [],
    this.deliveryFee = 0.0,
    this.serviceFee = 0.0,
    this.tax = 0.0,
    this.promoCode,
    this.discount = 0.0,
  });

  double get subtotal {
    return items.fold(0.0, (sum, item) => sum + item.totalPrice);
  }

  double get total {
    return subtotal + deliveryFee + serviceFee + tax - discount;
  }

  int get totalItems {
    return items.fold(0, (sum, item) => sum + item.quantity);
  }

  bool get isEmpty => items.isEmpty;

  bool get isNotEmpty => items.isNotEmpty;

  Cart addItem(CartItem item) {
    final existingItemIndex = items.indexWhere(
      (cartItem) =>
          cartItem.menuItemId == item.menuItemId &&
          _customizationsMatch(
              cartItem.selectedCustomizations, item.selectedCustomizations),
    );

    List<CartItem> updatedItems = List.from(items);

    if (existingItemIndex != -1) {
      updatedItems[existingItemIndex] =
          updatedItems[existingItemIndex].copyWith(
        quantity: updatedItems[existingItemIndex].quantity + item.quantity,
      );
    } else {
      updatedItems.add(item);
    }

    return copyWith(items: updatedItems);
  }

  Cart removeItem(String cartItemId) {
    final updatedItems = items.where((item) => item.id != cartItemId).toList();
    return copyWith(items: updatedItems);
  }

  Cart updateItemQuantity(String cartItemId, int quantity) {
    if (quantity <= 0) {
      return removeItem(cartItemId);
    }

    final updatedItems = items.map((item) {
      if (item.id == cartItemId) {
        return item.copyWith(quantity: quantity);
      }
      return item;
    }).toList();

    return copyWith(items: updatedItems);
  }

  Cart clear() {
    return copyWith(items: []);
  }

  Cart applyPromoCode(String code, double discountAmount) {
    return copyWith(promoCode: code, discount: discountAmount);
  }

  Cart removePromoCode() {
    return copyWith(promoCode: null, discount: 0.0);
  }

  bool _customizationsMatch(List<SelectedCustomization> customizations1,
      List<SelectedCustomization> customizations2) {
    if (customizations1.length != customizations2.length) return false;

    for (var customization1 in customizations1) {
      final match = customizations2.any((customization2) =>
          customization1.customizationId == customization2.customizationId &&
          customization1.selectedOptions.length ==
              customization2.selectedOptions.length &&
          customization1.selectedOptions.every((option1) => customization2
              .selectedOptions
              .any((option2) => option1.id == option2.id)));
      if (!match) return false;
    }

    return true;
  }

  Cart copyWith({
    String? restaurantId,
    String? restaurantName,
    List<CartItem>? items,
    double? deliveryFee,
    double? serviceFee,
    double? tax,
    String? promoCode,
    double? discount,
  }) {
    return Cart(
      restaurantId: restaurantId ?? this.restaurantId,
      restaurantName: restaurantName ?? this.restaurantName,
      items: items ?? this.items,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      serviceFee: serviceFee ?? this.serviceFee,
      tax: tax ?? this.tax,
      promoCode: promoCode ?? this.promoCode,
      discount: discount ?? this.discount,
    );
  }

  factory Cart.fromJson(Map<String, dynamic> json) {
    return Cart(
      restaurantId: json['restaurant_id'],
      restaurantName: json['restaurant_name'],
      items: (json['items'] as List<dynamic>?)
              ?.map((item) => CartItem.fromJson(item))
              .toList() ??
          [],
      deliveryFee: json['delivery_fee']?.toDouble() ?? 0.0,
      serviceFee: json['service_fee']?.toDouble() ?? 0.0,
      tax: json['tax']?.toDouble() ?? 0.0,
      promoCode: json['promo_code'],
      discount: json['discount']?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'restaurant_id': restaurantId,
      'restaurant_name': restaurantName,
      'items': items.map((item) => item.toJson()).toList(),
      'delivery_fee': deliveryFee,
      'service_fee': serviceFee,
      'tax': tax,
      'promo_code': promoCode,
      'discount': discount,
    };
  }
}

class CartItem {
  final String id;
  final String menuItemId;
  final String restaurantId;
  final String restaurantName;
  final String name;
  final String description;
  final double basePrice;
  final String? imageUrl;
  final int quantity;
  final List<SelectedCustomization> selectedCustomizations;
  final String? specialInstructions;

  CartItem({
    required this.id,
    required this.menuItemId,
    required this.restaurantId,
    required this.restaurantName,
    required this.name,
    required this.description,
    required this.basePrice,
    this.imageUrl,
    required this.quantity,
    this.selectedCustomizations = const [],
    this.specialInstructions,
  });

  double get customizationPrice {
    return selectedCustomizations.fold(0.0, (sum, customization) {
      return sum +
          customization.selectedOptions.fold(0.0, (optionSum, option) {
            return optionSum + option.additionalPrice;
          });
    });
  }

  double get unitPrice => basePrice + customizationPrice;

  double get totalPrice => unitPrice * quantity;

  double get price => unitPrice; // Alias for compatibility

  String get priceText => '\$${unitPrice.toStringAsFixed(2)}';

  String get totalPriceText => '\$${totalPrice.toStringAsFixed(2)}';

  CartItem copyWith({
    String? id,
    String? menuItemId,
    String? restaurantId,
    String? restaurantName,
    String? name,
    String? description,
    double? basePrice,
    String? imageUrl,
    int? quantity,
    List<SelectedCustomization>? selectedCustomizations,
    String? specialInstructions,
  }) {
    return CartItem(
      id: id ?? this.id,
      menuItemId: menuItemId ?? this.menuItemId,
      restaurantId: restaurantId ?? this.restaurantId,
      restaurantName: restaurantName ?? this.restaurantName,
      name: name ?? this.name,
      description: description ?? this.description,
      basePrice: basePrice ?? this.basePrice,
      imageUrl: imageUrl ?? this.imageUrl,
      quantity: quantity ?? this.quantity,
      selectedCustomizations:
          selectedCustomizations ?? this.selectedCustomizations,
      specialInstructions: specialInstructions ?? this.specialInstructions,
    );
  }

  factory CartItem.fromJson(Map<String, dynamic> json) {
    return CartItem(
      id: json['id'],
      menuItemId: json['menu_item_id'],
      restaurantId: json['restaurant_id'] ?? '',
      restaurantName: json['restaurant_name'] ?? '',
      name: json['name'],
      description: json['description'],
      basePrice: json['base_price'].toDouble(),
      imageUrl: json['image_url'],
      quantity: json['quantity'],
      selectedCustomizations:
          (json['selected_customizations'] as List<dynamic>?)
                  ?.map((customization) =>
                      SelectedCustomization.fromJson(customization))
                  .toList() ??
              [],
      specialInstructions: json['special_instructions'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'menu_item_id': menuItemId,
      'restaurant_id': restaurantId,
      'restaurant_name': restaurantName,
      'name': name,
      'description': description,
      'base_price': basePrice,
      'image_url': imageUrl,
      'quantity': quantity,
      'selected_customizations': selectedCustomizations
          .map((customization) => customization.toJson())
          .toList(),
      'special_instructions': specialInstructions,
    };
  }
}

class SelectedCustomization {
  final String customizationId;
  final String customizationName;
  final List<CustomizationOption> selectedOptions;

  SelectedCustomization({
    required this.customizationId,
    required this.customizationName,
    required this.selectedOptions,
  });

  factory SelectedCustomization.fromJson(Map<String, dynamic> json) {
    return SelectedCustomization(
      customizationId: json['customization_id'],
      customizationName: json['customization_name'],
      selectedOptions: (json['selected_options'] as List<dynamic>?)
              ?.map((option) => CustomizationOption.fromJson(option))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'customization_id': customizationId,
      'customization_name': customizationName,
      'selected_options':
          selectedOptions.map((option) => option.toJson()).toList(),
    };
  }
}

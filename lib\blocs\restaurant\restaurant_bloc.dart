import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';
import '../../models/restaurant.dart';
import 'restaurant_event.dart';
import 'restaurant_state.dart';

class RestaurantBloc extends Bloc<RestaurantEvent, RestaurantState> {
  RestaurantBloc() : super(RestaurantInitial()) {
    on<RestaurantLoadRequested>(_onRestaurantLoadRequested);
    on<RestaurantSearchRequested>(_onRestaurantSearchRequested);
    on<RestaurantFilterApplied>(_onRestaurantFilterApplied);
    on<RestaurantDetailsRequested>(_onRestaurantDetailsRequested);
    on<RestaurantCategorySelected>(_onRestaurantCategorySelected);
    on<RestaurantRefreshRequested>(_onRestaurantRefreshRequested);
    on<RestaurantLocationUpdated>(_onRestaurantLocationUpdated);
  }

  Future<void> _onRestaurantLoadRequested(
      RestaurantLoadRequested event, Emitter<RestaurantState> emit) async {
    emit(RestaurantLoading());

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      final restaurants = _getMockRestaurants();

      emit(RestaurantLoaded(
        restaurants: restaurants,
        filteredRestaurants: restaurants,
      ));
    } catch (e) {
      debugPrint('Error loading restaurants: $e');
      emit(const RestaurantError(message: 'Failed to load restaurants'));
    }
  }

  Future<void> _onRestaurantSearchRequested(
      RestaurantSearchRequested event, Emitter<RestaurantState> emit) async {
    final currentState = state;
    if (currentState is RestaurantLoaded) {
      emit(RestaurantLoading());

      try {
        await Future.delayed(const Duration(milliseconds: 500));

        final results = currentState.restaurants.where((restaurant) {
          return restaurant.name
                  .toLowerCase()
                  .contains(event.query.toLowerCase()) ||
              restaurant.cuisine
                  .toLowerCase()
                  .contains(event.query.toLowerCase()) ||
              restaurant.tags.any((tag) =>
                  tag.toLowerCase().contains(event.query.toLowerCase()));
        }).toList();

        emit(RestaurantSearchResults(results: results, query: event.query));
      } catch (e) {
        emit(const RestaurantError(message: 'Search failed'));
      }
    }
  }

  Future<void> _onRestaurantFilterApplied(
      RestaurantFilterApplied event, Emitter<RestaurantState> emit) async {
    final currentState = state;
    if (currentState is RestaurantLoaded) {
      emit(RestaurantLoading());

      try {
        await Future.delayed(const Duration(milliseconds: 300));

        var filteredRestaurants =
            List<Restaurant>.from(currentState.restaurants);

        if (event.cuisine != null && event.cuisine != 'All') {
          filteredRestaurants = filteredRestaurants
              .where((r) =>
                  r.cuisine.toLowerCase() == event.cuisine!.toLowerCase())
              .toList();
        }

        if (event.minRating != null) {
          filteredRestaurants = filteredRestaurants
              .where((r) => r.rating >= event.minRating!)
              .toList();
        }

        if (event.maxDeliveryTime != null) {
          filteredRestaurants = filteredRestaurants
              .where((r) =>
                  r.estimatedDeliveryTime <= event.maxDeliveryTime!.toInt())
              .toList();
        }

        if (event.freeDelivery == true) {
          filteredRestaurants =
              filteredRestaurants.where((r) => r.deliveryFee == 0.0).toList();
        }

        if (event.hasOffers == true) {
          filteredRestaurants = filteredRestaurants
              .where((r) => (r.discountPercentage ?? 0) > 0)
              .toList();
        }

        final filters = {
          'cuisine': event.cuisine,
          'minRating': event.minRating,
          'maxDeliveryTime': event.maxDeliveryTime,
          'freeDelivery': event.freeDelivery,
          'hasOffers': event.hasOffers,
          'vegetarianOnly': event.vegetarianOnly,
        };

        emit(RestaurantFilterResults(
            results: filteredRestaurants, filters: filters));
      } catch (e) {
        emit(const RestaurantError(message: 'Filter failed'));
      }
    }
  }

  Future<void> _onRestaurantDetailsRequested(
      RestaurantDetailsRequested event, Emitter<RestaurantState> emit) async {
    emit(RestaurantLoading());

    try {
      await Future.delayed(const Duration(milliseconds: 500));

      final restaurant =
          _getMockRestaurants().firstWhere((r) => r.id == event.restaurantId);

      emit(RestaurantDetailsLoaded(restaurant: restaurant));
    } catch (e) {
      emit(const RestaurantError(message: 'Failed to load restaurant details'));
    }
  }

  Future<void> _onRestaurantCategorySelected(
      RestaurantCategorySelected event, Emitter<RestaurantState> emit) async {
    final currentState = state;
    if (currentState is RestaurantLoaded) {
      var filteredRestaurants = List<Restaurant>.from(currentState.restaurants);

      if (event.category != 'All') {
        filteredRestaurants = filteredRestaurants
            .where((r) =>
                r.cuisine.toLowerCase() == event.category.toLowerCase() ||
                r.tags.any(
                    (tag) => tag.toLowerCase() == event.category.toLowerCase()))
            .toList();
      }

      emit(currentState.copyWith(
        filteredRestaurants: filteredRestaurants,
        selectedCategory: event.category,
      ));
    }
  }

  Future<void> _onRestaurantRefreshRequested(
      RestaurantRefreshRequested event, Emitter<RestaurantState> emit) async {
    add(RestaurantLoadRequested());
  }

  Future<void> _onRestaurantLocationUpdated(
      RestaurantLocationUpdated event, Emitter<RestaurantState> emit) async {
    // In a real app, this would filter restaurants by location
    add(RestaurantLoadRequested());
  }

  List<Restaurant> _getMockRestaurants() {
    return [
      Restaurant(
        id: '1',
        name: 'Pizza Palace',
        description: 'Authentic Italian pizza and pasta',
        cuisine: 'Italian',
        imageUrl:
            'https://via.placeholder.com/300x200/FF6B6B/FFFFFF?text=Pizza+Palace',
        rating: 4.5,
        reviewCount: 1250,
        address: '123 Main St, Downtown',
        latitude: 40.7128,
        longitude: -74.0060,
        phone: '+1234567890',
        email: '<EMAIL>',
        estimatedDeliveryTime: 30,
        minimumOrderValue: 15.0,
        deliveryFee: 2.99,
        isOpen: true,
        openingHours: '10:00 AM',
        closingHours: '11:00 PM',
        tags: ['Featured', 'Popular', 'Fast Food'],
        discountPercentage: 20.0,
        menuCategories: [],
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
      ),
      Restaurant(
        id: '2',
        name: 'Burger Barn',
        description: 'Juicy burgers and crispy fries',
        cuisine: 'American',
        imageUrl:
            'https://via.placeholder.com/300x200/4ECDC4/FFFFFF?text=Burger+Barn',
        rating: 4.2,
        reviewCount: 890,
        address: '456 Oak Ave, Midtown',
        latitude: 40.7589,
        longitude: -73.9851,
        phone: '+1234567891',
        email: '<EMAIL>',
        estimatedDeliveryTime: 25,
        minimumOrderValue: 12.0,
        deliveryFee: 1.99,
        isOpen: true,
        openingHours: '11:00 AM',
        closingHours: '10:00 PM',
        tags: ['Popular', 'Fast Food'],
        discountPercentage: 15.0,
        menuCategories: [],
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
      ),
      Restaurant(
        id: '3',
        name: 'Spice Garden',
        description: 'Authentic Indian cuisine with aromatic spices',
        cuisine: 'Indian',
        imageUrl:
            'https://via.placeholder.com/300x200/45B7D1/FFFFFF?text=Spice+Garden',
        rating: 4.7,
        reviewCount: 2100,
        address: '789 Curry Lane, Little India',
        latitude: 40.7505,
        longitude: -73.9934,
        phone: '+1234567892',
        email: '<EMAIL>',
        estimatedDeliveryTime: 35,
        minimumOrderValue: 20.0,
        deliveryFee: 3.99,
        isOpen: true,
        openingHours: '12:00 PM',
        closingHours: '11:30 PM',
        tags: ['Featured', 'Authentic', 'Spicy'],
        discountPercentage: 10.0,
        menuCategories: [],
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
      ),
      Restaurant(
        id: '4',
        name: 'Dragon Wok',
        description: 'Traditional Chinese dishes and dim sum',
        cuisine: 'Chinese',
        imageUrl:
            'https://via.placeholder.com/300x200/F7DC6F/FFFFFF?text=Dragon+Wok',
        rating: 4.3,
        reviewCount: 1560,
        address: '321 Bamboo St, Chinatown',
        latitude: 40.7155,
        longitude: -73.9976,
        phone: '+1234567893',
        email: '<EMAIL>',
        estimatedDeliveryTime: 28,
        minimumOrderValue: 18.0,
        deliveryFee: 2.49,
        isOpen: true,
        openingHours: '11:30 AM',
        closingHours: '10:30 PM',
        tags: ['Popular', 'Authentic'],
        discountPercentage: 0.0,
        menuCategories: [],
      ),
      Restaurant(
        id: '5',
        name: 'Taco Fiesta',
        cuisine: 'Mexican',
        imageUrl:
            'https://via.placeholder.com/300x200/E74C3C/FFFFFF?text=Taco+Fiesta',
        rating: 4.1,
        reviewCount: 750,
        address: '654 Salsa Ave, Mexican Quarter',
        latitude: 40.7282,
        longitude: -73.9942,
        phone: '+1234567894',
        email: '<EMAIL>',
        estimatedDeliveryTime: 22,
        minimumOrderValue: 10.0,
        deliveryFee: 1.49,
        isOpen: true,
        openingHours: '10:30 AM',
        closingHours: '11:00 PM',
        tags: ['Fast Food', 'Spicy'],
        discountPercentage: 25.0,
        menuCategories: [],
      ),
    ];
  }
}

import 'package:equatable/equatable.dart';
import '../../models/order.dart';

abstract class OrderState extends Equatable {
  const OrderState();

  @override
  List<Object?> get props => [];
}

class OrderInitial extends OrderState {}

class OrderLoading extends OrderState {}

class OrderPlaced extends OrderState {
  final Order order;

  const OrderPlaced({required this.order});

  @override
  List<Object> get props => [order];
}

class OrderHistoryLoaded extends OrderState {
  final List<Order> orders;

  const OrderHistoryLoaded({required this.orders});

  @override
  List<Object> get props => [orders];
}

class OrderDetailsLoaded extends OrderState {
  final Order order;

  const OrderDetailsLoaded({required this.order});

  @override
  List<Object> get props => [order];
}

class OrderTrackingLoaded extends OrderState {
  final Order order;
  final List<OrderStatusUpdate> statusUpdates;
  final DeliveryInfo? deliveryInfo;

  const OrderTrackingLoaded({
    required this.order,
    required this.statusUpdates,
    this.deliveryInfo,
  });

  @override
  List<Object?> get props => [order, statusUpdates, deliveryInfo];
}

class OrderCancelled extends OrderState {
  final String orderId;
  final String reason;

  const OrderCancelled({
    required this.orderId,
    required this.reason,
  });

  @override
  List<Object> get props => [orderId, reason];
}

class OrderRated extends OrderState {
  final String orderId;
  final double rating;
  final String? review;

  const OrderRated({
    required this.orderId,
    required this.rating,
    this.review,
  });

  @override
  List<Object?> get props => [orderId, rating, review];
}

class OrderReordered extends OrderState {
  final Order originalOrder;
  final List<dynamic> cartItems; // Items added to cart

  const OrderReordered({
    required this.originalOrder,
    required this.cartItems,
  });

  @override
  List<Object> get props => [originalOrder, cartItems];
}

class OrderError extends OrderState {
  final String message;

  const OrderError({required this.message});

  @override
  List<Object> get props => [message];
}

class OrderPaymentProcessing extends OrderState {
  final String orderId;
  final String paymentMethod;

  const OrderPaymentProcessing({
    required this.orderId,
    required this.paymentMethod,
  });

  @override
  List<Object> get props => [orderId, paymentMethod];
}

class OrderPaymentSuccess extends OrderState {
  final Order order;
  final String transactionId;

  const OrderPaymentSuccess({
    required this.order,
    required this.transactionId,
  });

  @override
  List<Object> get props => [order, transactionId];
}

class OrderPaymentFailed extends OrderState {
  final String message;
  final String? orderId;

  const OrderPaymentFailed({
    required this.message,
    this.orderId,
  });

  @override
  List<Object?> get props => [message, orderId];
}

// Additional classes for order tracking
class DeliveryInfo {
  final String deliveryPersonName;
  final String deliveryPersonPhone;
  final String? deliveryPersonImage;
  final double? currentLatitude;
  final double? currentLongitude;
  final String? vehicleNumber;

  const DeliveryInfo({
    required this.deliveryPersonName,
    required this.deliveryPersonPhone,
    this.deliveryPersonImage,
    this.currentLatitude,
    this.currentLongitude,
    this.vehicleNumber,
  });
}

class OrderStatusUpdate {
  final OrderStatus status;
  final DateTime timestamp;
  final String description;

  const OrderStatusUpdate({
    required this.status,
    required this.timestamp,
    required this.description,
  });
}

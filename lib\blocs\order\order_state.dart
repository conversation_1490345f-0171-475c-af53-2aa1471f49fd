import 'package:equatable/equatable.dart';

// Simple Order classes for Bloc state management
class SimpleOrder {
  final String id;
  final String userId;
  final String restaurantId;
  final String restaurantName;
  final List<SimpleOrderItem> items;
  final String status;
  final double total;
  final String deliveryAddress;
  final String paymentMethod;
  final String? specialInstructions;
  final String? promoCode;
  final DateTime orderDate;
  final DateTime? estimatedDeliveryTime;
  final DateTime? actualDeliveryTime;

  const SimpleOrder({
    required this.id,
    required this.userId,
    required this.restaurantId,
    required this.restaurantName,
    required this.items,
    required this.status,
    required this.total,
    required this.deliveryAddress,
    required this.paymentMethod,
    this.specialInstructions,
    this.promoCode,
    required this.orderDate,
    this.estimatedDeliveryTime,
    this.actualDeliveryTime,
  });
}

class SimpleOrderItem {
  final String id;
  final String name;
  final double price;
  final int quantity;
  final String? imageUrl;
  final List<String> customizations;

  const SimpleOrderItem({
    required this.id,
    required this.name,
    required this.price,
    required this.quantity,
    this.imageUrl,
    this.customizations = const [],
  });
}

abstract class OrderState extends Equatable {
  const OrderState();

  @override
  List<Object?> get props => [];
}

class OrderInitial extends OrderState {}

class OrderLoading extends OrderState {}

class OrderPlaced extends OrderState {
  final SimpleOrder order;

  const OrderPlaced({required this.order});

  @override
  List<Object> get props => [order];
}

class OrderHistoryLoaded extends OrderState {
  final List<SimpleOrder> orders;

  const OrderHistoryLoaded({required this.orders});

  @override
  List<Object> get props => [orders];
}

class OrderDetailsLoaded extends OrderState {
  final SimpleOrder order;

  const OrderDetailsLoaded({required this.order});

  @override
  List<Object> get props => [order];
}

class OrderTrackingLoaded extends OrderState {
  final SimpleOrder order;
  final List<SimpleOrderStatusUpdate> statusUpdates;
  final DeliveryInfo? deliveryInfo;

  const OrderTrackingLoaded({
    required this.order,
    required this.statusUpdates,
    this.deliveryInfo,
  });

  @override
  List<Object?> get props => [order, statusUpdates, deliveryInfo];
}

class OrderCancelled extends OrderState {
  final String orderId;
  final String reason;

  const OrderCancelled({
    required this.orderId,
    required this.reason,
  });

  @override
  List<Object> get props => [orderId, reason];
}

class OrderRated extends OrderState {
  final String orderId;
  final double rating;
  final String? review;

  const OrderRated({
    required this.orderId,
    required this.rating,
    this.review,
  });

  @override
  List<Object?> get props => [orderId, rating, review];
}

class OrderReordered extends OrderState {
  final SimpleOrder originalOrder;
  final List<dynamic> cartItems; // Items added to cart

  const OrderReordered({
    required this.originalOrder,
    required this.cartItems,
  });

  @override
  List<Object> get props => [originalOrder, cartItems];
}

class OrderError extends OrderState {
  final String message;

  const OrderError({required this.message});

  @override
  List<Object> get props => [message];
}

class OrderPaymentProcessing extends OrderState {
  final String orderId;
  final String paymentMethod;

  const OrderPaymentProcessing({
    required this.orderId,
    required this.paymentMethod,
  });

  @override
  List<Object> get props => [orderId, paymentMethod];
}

class OrderPaymentSuccess extends OrderState {
  final SimpleOrder order;
  final String transactionId;

  const OrderPaymentSuccess({
    required this.order,
    required this.transactionId,
  });

  @override
  List<Object> get props => [order, transactionId];
}

class OrderPaymentFailed extends OrderState {
  final String message;
  final String? orderId;

  const OrderPaymentFailed({
    required this.message,
    this.orderId,
  });

  @override
  List<Object?> get props => [message, orderId];
}

// Additional classes for order tracking
class DeliveryInfo {
  final String deliveryPersonName;
  final String deliveryPersonPhone;
  final String? deliveryPersonImage;
  final double? currentLatitude;
  final double? currentLongitude;
  final String? vehicleNumber;

  const DeliveryInfo({
    required this.deliveryPersonName,
    required this.deliveryPersonPhone,
    this.deliveryPersonImage,
    this.currentLatitude,
    this.currentLongitude,
    this.vehicleNumber,
  });
}

class SimpleOrderStatusUpdate {
  final String status;
  final DateTime timestamp;
  final String description;

  const SimpleOrderStatusUpdate({
    required this.status,
    required this.timestamp,
    required this.description,
  });
}

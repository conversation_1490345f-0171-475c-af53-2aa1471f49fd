import 'package:equatable/equatable.dart';
import '../../models/cart.dart';

abstract class CartState extends Equatable {
  const CartState();

  @override
  List<Object?> get props => [];
}

class CartInitial extends CartState {}

class CartLoading extends CartState {}

class CartLoaded extends CartState {
  final List<CartItem> items;
  final double subtotal;
  final double deliveryFee;
  final double tax;
  final double discount;
  final double total;
  final int itemCount;
  final String? promoCode;
  final String? deliveryAddress;
  final String? specialInstructions;

  const CartLoaded({
    required this.items,
    required this.subtotal,
    required this.deliveryFee,
    required this.tax,
    required this.discount,
    required this.total,
    required this.itemCount,
    this.promoCode,
    this.deliveryAddress,
    this.specialInstructions,
  });

  @override
  List<Object?> get props => [
        items,
        subtotal,
        deliveryFee,
        tax,
        discount,
        total,
        itemCount,
        promoCode,
        deliveryAddress,
        specialInstructions,
      ];

  CartLoaded copyWith({
    List<CartItem>? items,
    double? subtotal,
    double? deliveryFee,
    double? tax,
    double? discount,
    double? total,
    int? itemCount,
    String? promoCode,
    String? deliveryAddress,
    String? specialInstructions,
  }) {
    return CartLoaded(
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      tax: tax ?? this.tax,
      discount: discount ?? this.discount,
      total: total ?? this.total,
      itemCount: itemCount ?? this.itemCount,
      promoCode: promoCode ?? this.promoCode,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      specialInstructions: specialInstructions ?? this.specialInstructions,
    );
  }
}

class CartError extends CartState {
  final String message;

  const CartError({required this.message});

  @override
  List<Object> get props => [message];
}

class CartPromoCodeAppliedState extends CartState {
  final String promoCode;
  final double discountAmount;

  const CartPromoCodeAppliedState({
    required this.promoCode,
    required this.discountAmount,
  });

  @override
  List<Object> get props => [promoCode, discountAmount];
}

class CartPromoCodeError extends CartState {
  final String message;

  const CartPromoCodeError({required this.message});

  @override
  List<Object> get props => [message];
}

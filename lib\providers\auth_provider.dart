import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import '../utils/constants.dart';

class AuthProvider with ChangeNotifier {
  User? _user;
  bool _isLoading = false;
  bool _isLoggedIn = false;
  String? _token;

  User? get user => _user;
  bool get isLoading => _isLoading;
  bool get isLoggedIn => _isLoggedIn;
  String? get token => _token;

  AuthProvider() {
    _loadUserFromStorage();
  }

  Future<void> _loadUserFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(AppConstants.userTokenKey);
      final userData = prefs.getString(AppConstants.userDataKey);

      if (token != null && userData != null) {
        _token = token;
        _user = User.fromJson(_parseJson(userData));
        _isLoggedIn = true;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading user from storage: $e');
    }
  }

  Map<String, dynamic> _parseJson(String jsonString) {
    // In a real app, you would use dart:convert
    // For now, returning a mock user data structure
    return {
      'id': '1',
      'name': 'John Doe',
      'email': '<EMAIL>',
      'phone': '+**********',
      'profile_image': null,
      'addresses': [],
      'created_at': DateTime.now().toIso8601String(),
    };
  }

  Future<bool> login(String email, String password) async {
    _setLoading(true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Mock successful login
      if (email.isNotEmpty && password.isNotEmpty) {
        final mockUser = User(
          id: '1',
          name: 'John Doe',
          email: email,
          phone: '+**********',
          createdAt: DateTime.now(),
        );

        const mockToken = 'mock_jwt_token_12345';

        await _saveUserToStorage(mockUser, mockToken);

        _user = mockUser;
        _token = mockToken;
        _isLoggedIn = true;

        _setLoading(false);
        return true;
      } else {
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setLoading(false);
      debugPrint('Login error: $e');
      return false;
    }
  }

  Future<bool> signup(
      String name, String email, String phone, String password) async {
    _setLoading(true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Mock successful signup
      if (name.isNotEmpty && email.isNotEmpty && password.isNotEmpty) {
        final mockUser = User(
          id: '1',
          name: name,
          email: email,
          phone: phone,
          createdAt: DateTime.now(),
        );

        const mockToken = 'mock_jwt_token_12345';

        await _saveUserToStorage(mockUser, mockToken);

        _user = mockUser;
        _token = mockToken;
        _isLoggedIn = true;

        _setLoading(false);
        return true;
      } else {
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setLoading(false);
      debugPrint('Signup error: $e');
      return false;
    }
  }

  Future<bool> loginWithGoogle() async {
    _setLoading(true);

    try {
      // Simulate Google login
      await Future.delayed(const Duration(seconds: 2));

      final mockUser = User(
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        profileImage: 'https://example.com/profile.jpg',
        createdAt: DateTime.now(),
      );

      const mockToken = 'mock_google_jwt_token_12345';

      await _saveUserToStorage(mockUser, mockToken);

      _user = mockUser;
      _token = mockToken;
      _isLoggedIn = true;

      _setLoading(false);
      return true;
    } catch (e) {
      _setLoading(false);
      debugPrint('Google login error: $e');
      return false;
    }
  }

  Future<bool> loginWithFacebook() async {
    _setLoading(true);

    try {
      // Simulate Facebook login
      await Future.delayed(const Duration(seconds: 2));

      final mockUser = User(
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        profileImage: 'https://example.com/profile.jpg',
        createdAt: DateTime.now(),
      );

      const mockToken = 'mock_facebook_jwt_token_12345';

      await _saveUserToStorage(mockUser, mockToken);

      _user = mockUser;
      _token = mockToken;
      _isLoggedIn = true;

      _setLoading(false);
      return true;
    } catch (e) {
      _setLoading(false);
      debugPrint('Facebook login error: $e');
      return false;
    }
  }

  Future<bool> forgotPassword(String email) async {
    _setLoading(true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      _setLoading(false);
      return email.isNotEmpty;
    } catch (e) {
      _setLoading(false);
      debugPrint('Forgot password error: $e');
      return false;
    }
  }

  Future<bool> resetPassword(String email) async {
    _setLoading(true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      _setLoading(false);
      return email.isNotEmpty;
    } catch (e) {
      _setLoading(false);
      debugPrint('Reset password error: $e');
      return false;
    }
  }

  Future<void> logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConstants.userTokenKey);
      await prefs.remove(AppConstants.userDataKey);

      _user = null;
      _token = null;
      _isLoggedIn = false;

      notifyListeners();
    } catch (e) {
      debugPrint('Logout error: $e');
    }
  }

  Future<bool> updateProfile(User updatedUser) async {
    _setLoading(true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      await _saveUserToStorage(updatedUser, _token!);

      _user = updatedUser;
      _setLoading(false);
      return true;
    } catch (e) {
      _setLoading(false);
      debugPrint('Update profile error: $e');
      return false;
    }
  }

  Future<bool> changePassword(
      String currentPassword, String newPassword) async {
    _setLoading(true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      _setLoading(false);
      return currentPassword.isNotEmpty && newPassword.isNotEmpty;
    } catch (e) {
      _setLoading(false);
      debugPrint('Change password error: $e');
      return false;
    }
  }

  Future<void> _saveUserToStorage(User user, String token) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.userTokenKey, token);
      await prefs.setString(AppConstants.userDataKey, _userToJsonString(user));
    } catch (e) {
      debugPrint('Error saving user to storage: $e');
    }
  }

  String _userToJsonString(User user) {
    // In a real app, you would use dart:convert
    // For now, returning a mock JSON string
    return '{"id":"${user.id}","name":"${user.name}","email":"${user.email}"}';
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Add address to user
  Future<bool> addAddress(Address address) async {
    if (_user == null) return false;

    _setLoading(true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      final updatedAddresses = List<Address>.from(_user!.addresses)
        ..add(address);
      final updatedUser = _user!.copyWith(addresses: updatedAddresses);

      await _saveUserToStorage(updatedUser, _token!);
      _user = updatedUser;

      _setLoading(false);
      return true;
    } catch (e) {
      _setLoading(false);
      debugPrint('Add address error: $e');
      return false;
    }
  }

  // Update address
  Future<bool> updateAddress(Address address) async {
    if (_user == null) return false;

    _setLoading(true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      final updatedAddresses = _user!.addresses.map((addr) {
        return addr.id == address.id ? address : addr;
      }).toList();

      final updatedUser = _user!.copyWith(addresses: updatedAddresses);

      await _saveUserToStorage(updatedUser, _token!);
      _user = updatedUser;

      _setLoading(false);
      return true;
    } catch (e) {
      _setLoading(false);
      debugPrint('Update address error: $e');
      return false;
    }
  }

  // Delete address
  Future<bool> deleteAddress(String addressId) async {
    if (_user == null) return false;

    _setLoading(true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      final updatedAddresses =
          _user!.addresses.where((addr) => addr.id != addressId).toList();
      final updatedUser = _user!.copyWith(addresses: updatedAddresses);

      await _saveUserToStorage(updatedUser, _token!);
      _user = updatedUser;

      _setLoading(false);
      return true;
    } catch (e) {
      _setLoading(false);
      debugPrint('Delete address error: $e');
      return false;
    }
  }
}

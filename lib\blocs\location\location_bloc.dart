import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import '../../utils/constants.dart';
import 'location_event.dart';
import 'location_state.dart';

class LocationBloc extends Bloc<LocationEvent, LocationState> {
  LocationBloc() : super(LocationInitial()) {
    on<LocationStarted>(_onLocationStarted);
    on<LocationPermissionRequested>(_onLocationPermissionRequested);
    on<LocationCurrentRequested>(_onLocationCurrentRequested);
    on<LocationAddressSelected>(_onLocationAddressSelected);
    on<LocationAddressAdded>(_onLocationAddressAdded);
    on<LocationAddressUpdated>(_onLocationAddressUpdated);
    on<LocationAddressDeleted>(_onLocationAddressDeleted);
    on<LocationSavedAddressesRequested>(_onLocationSavedAddressesRequested);
    on<LocationAddressSearchRequested>(_onLocationAddressSearchRequested);
    on<LocationMapPositionChanged>(_onLocationMapPositionChanged);
  }

  Future<void> _onLocationStarted(
      LocationStarted event, Emitter<LocationState> emit) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedAddress = prefs.getString(AppConstants.currentLocationKey);
      
      if (savedAddress != null) {
        // Load saved location
        emit(const LocationLoaded(
          address: '123 Main St, New York, NY',
          latitude: 40.7128,
          longitude: -74.0060,
        ));
      } else {
        emit(LocationInitial());
      }
    } catch (e) {
      debugPrint('Error starting location: $e');
      emit(const LocationError(message: 'Failed to initialize location'));
    }
  }

  Future<void> _onLocationPermissionRequested(
      LocationPermissionRequested event, Emitter<LocationState> emit) async {
    emit(LocationLoading());

    try {
      final permission = await Permission.location.request();
      
      if (permission.isGranted) {
        emit(LocationPermissionGranted());
      } else if (permission.isDenied) {
        emit(const LocationPermissionDenied(
          message: 'Location permission is required to find nearby restaurants',
        ));
      } else if (permission.isPermanentlyDenied) {
        emit(const LocationPermissionDenied(
          message: 'Location permission is permanently denied. Please enable it in settings.',
        ));
      }
    } catch (e) {
      debugPrint('Error requesting location permission: $e');
      emit(const LocationError(message: 'Failed to request location permission'));
    }
  }

  Future<void> _onLocationCurrentRequested(
      LocationCurrentRequested event, Emitter<LocationState> emit) async {
    emit(LocationLoading());

    try {
      final permission = await Geolocator.checkPermission();
      
      if (permission == LocationPermission.denied) {
        final requestedPermission = await Geolocator.requestPermission();
        if (requestedPermission == LocationPermission.denied) {
          emit(const LocationPermissionDenied(
            message: 'Location permission is required',
          ));
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        emit(const LocationPermissionDenied(
          message: 'Location permission is permanently denied',
        ));
        return;
      }

      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Mock reverse geocoding
      const address = '123 Current Location St, New York, NY';

      await _saveCurrentLocation(address, position.latitude, position.longitude);

      emit(LocationLoaded(
        address: address,
        latitude: position.latitude,
        longitude: position.longitude,
      ));
    } catch (e) {
      debugPrint('Error getting current location: $e');
      emit(const LocationError(message: 'Failed to get current location'));
    }
  }

  Future<void> _onLocationAddressSelected(
      LocationAddressSelected event, Emitter<LocationState> emit) async {
    try {
      await _saveCurrentLocation(event.address, event.latitude, event.longitude);

      emit(LocationSelected(
        address: event.address,
        latitude: event.latitude,
        longitude: event.longitude,
      ));
    } catch (e) {
      debugPrint('Error selecting address: $e');
      emit(const LocationError(message: 'Failed to select address'));
    }
  }

  Future<void> _onLocationAddressAdded(
      LocationAddressAdded event, Emitter<LocationState> emit) async {
    emit(LocationLoading());

    try {
      await Future.delayed(const Duration(milliseconds: 500));

      // In a real app, save to backend/local storage
      emit(LocationAddressAdded(address: event.address));
    } catch (e) {
      debugPrint('Error adding address: $e');
      emit(const LocationError(message: 'Failed to add address'));
    }
  }

  Future<void> _onLocationAddressUpdated(
      LocationAddressUpdated event, Emitter<LocationState> emit) async {
    emit(LocationLoading());

    try {
      await Future.delayed(const Duration(milliseconds: 500));

      emit(LocationAddressUpdated(address: event.address));
    } catch (e) {
      debugPrint('Error updating address: $e');
      emit(const LocationError(message: 'Failed to update address'));
    }
  }

  Future<void> _onLocationAddressDeleted(
      LocationAddressDeleted event, Emitter<LocationState> emit) async {
    emit(LocationLoading());

    try {
      await Future.delayed(const Duration(milliseconds: 500));

      emit(LocationAddressDeleted(addressId: event.addressId));
    } catch (e) {
      debugPrint('Error deleting address: $e');
      emit(const LocationError(message: 'Failed to delete address'));
    }
  }

  Future<void> _onLocationSavedAddressesRequested(
      LocationSavedAddressesRequested event, Emitter<LocationState> emit) async {
    emit(LocationLoading());

    try {
      await Future.delayed(const Duration(milliseconds: 500));

      final addresses = _getMockSavedAddresses();
      final currentAddress = addresses.isNotEmpty ? addresses.first : null;

      emit(LocationSavedAddressesLoaded(
        addresses: addresses,
        currentAddress: currentAddress,
      ));
    } catch (e) {
      debugPrint('Error loading saved addresses: $e');
      emit(const LocationError(message: 'Failed to load saved addresses'));
    }
  }

  Future<void> _onLocationAddressSearchRequested(
      LocationAddressSearchRequested event, Emitter<LocationState> emit) async {
    emit(LocationLoading());

    try {
      await Future.delayed(const Duration(milliseconds: 800));

      final results = _getMockSearchResults(event.query);

      emit(LocationSearchResults(results: results, query: event.query));
    } catch (e) {
      debugPrint('Error searching addresses: $e');
      emit(const LocationError(message: 'Failed to search addresses'));
    }
  }

  Future<void> _onLocationMapPositionChanged(
      LocationMapPositionChanged event, Emitter<LocationState> emit) async {
    try {
      // Mock reverse geocoding
      const address = 'Selected Location Address';

      emit(LocationMapPositionUpdated(
        latitude: event.latitude,
        longitude: event.longitude,
        address: address,
      ));
    } catch (e) {
      debugPrint('Error updating map position: $e');
      emit(const LocationError(message: 'Failed to update location'));
    }
  }

  Future<void> _saveCurrentLocation(String address, double lat, double lng) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(AppConstants.currentLocationKey, address);
      await prefs.setDouble('current_latitude', lat);
      await prefs.setDouble('current_longitude', lng);
    } catch (e) {
      debugPrint('Error saving current location: $e');
    }
  }

  List<SavedAddress> _getMockSavedAddresses() {
    return [
      const SavedAddress(
        id: '1',
        label: 'Home',
        address: '123 Main St, Apt 4B, New York, NY 10001',
        landmark: 'Near Central Park',
        latitude: 40.7128,
        longitude: -74.0060,
        isDefault: true,
      ),
      const SavedAddress(
        id: '2',
        label: 'Work',
        address: '456 Business Ave, Suite 200, New York, NY 10002',
        landmark: 'Next to Starbucks',
        latitude: 40.7589,
        longitude: -73.9851,
        isDefault: false,
      ),
      const SavedAddress(
        id: '3',
        label: 'Other',
        address: '789 Friend St, Brooklyn, NY 11201',
        latitude: 40.6892,
        longitude: -73.9442,
        isDefault: false,
      ),
    ];
  }

  List<AddressSearchResult> _getMockSearchResults(String query) {
    return [
      AddressSearchResult(
        address: '$query Street, New York, NY',
        description: 'Main street in downtown',
        latitude: 40.7128,
        longitude: -74.0060,
      ),
      AddressSearchResult(
        address: '$query Avenue, Brooklyn, NY',
        description: 'Popular avenue in Brooklyn',
        latitude: 40.6892,
        longitude: -73.9442,
      ),
      AddressSearchResult(
        address: '$query Plaza, Manhattan, NY',
        description: 'Shopping plaza in Manhattan',
        latitude: 40.7505,
        longitude: -73.9934,
      ),
    ];
  }
}
